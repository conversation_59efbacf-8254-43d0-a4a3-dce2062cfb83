Dokumentasi Frontend KirimPintar untuk Engineer
<PERSON><PERSON><PERSON> ini berfungsi sebagai panduan komprehensif bagi frontend engineer yang akan berkontribusi pada pengembangan KirimPintar. KirimPintar adalah aplikasi CRM email Kanban self-hosted yang berfokus pada efisiensi dan kecaerdasan.

Tujuan dokumen ini:

Memberikan pemahaman mendalam tentang stack teknologi frontend KirimPintar.

Menguraikan arsitektur dan interaksi dengan backend.

Memberikan panduan untuk pengaturan lingkungan pengembangan.

Menjelaskan struktur proyek dan praktik terbaik.

Memfasilitasi pengembangan dan pemeliharaan fitur frontend.

1. Stack Teknologi Frontend
Frontend KirimPintar dibangun menggunakan Vue.js, sebuah framework progresif untuk membangun antarmuka pengguna.

1.1 Vue.js
Mengapa Vue.js?

Kemudahan Belajar: Vue.js dikenal memiliki kurva belajar yang relatif landai, memungkinkan developer untuk cepat produktif.

Performa Optimal: <PERSON><PERSON> dan cepat, memastikan UI yang responsif dan pengalaman pengguna yang mulus saat berinteraksi dengan papan Kanban.

Fleksibilitas: Dapat diskalakan dari proyek kecil hingga aplikasi kompleks, memberikan kebebasan dalam arsitektur komponen.

Komunitas Aktif: Dukungan komunitas yang besar dan ekosistem library yang terus berkembang.

1.2 Library & Tools Utama
Vue Router: Untuk manajemen routing di sisi klien (navigasi antar halaman/tampilan).

Pinia: Untuk manajemen state terpusat di seluruh aplikasi. Pinia direkomendasikan sebagai pengganti Vuex untuk proyek baru karena lebih ringan, type-safe, dan lebih mudah digunakan.

Axios: Klien HTTP berbasis Promise untuk membuat permintaan ke API backend.

Tailwind CSS: Framework CSS utility-first untuk styling yang cepat dan konsisten.

Vite: Build tool modern yang cepat untuk pengembangan frontend.

2. Arsitektur Frontend
Frontend KirimPintar beroperasi sebagai Single Page Application (SPA) yang berkomunikasi dengan backend Python melalui RESTful API. Desain arsitektur ini menekankan pada modularitas, skalabilitas, dan pengalaman pengguna yang responsif.

2.1 Interaksi dengan Backend API
Frontend akan mengirimkan permintaan HTTP (GET, POST, PUT, DELETE) ke endpoint API yang disediakan oleh backend Python (FastAPI/Django).

Semua data yang ditampilkan di UI (misalnya, daftar email, detail kartu, pengaturan kolom) akan diambil dari API.

Setiap tindakan pengguna (misalnya, memindahkan kartu, membalas email, mengubah pengaturan) akan dikirim sebagai permintaan ke API.

Otentikasi: Komunikasi API akan diamankan menggunakan token otentikasi (misalnya, JWT) yang dikelola di sisi frontend dan dikirimkan pada setiap permintaan yang diautentikasi.

Penanganan Kesalahan API: Modul src/services/api.js akan mencakup interceptor Axios untuk menangani kesalahan API secara global (misalnya, refresh token otomatis, penanganan error 401 Unauthorized, menampilkan pesan error umum).

2.2 Arsitektur Komponen
Aplikasi Vue.js akan mengikuti arsitektur berbasis komponen dengan pendekatan Atomic Design untuk memastikan reusabilitas, konsistensi, dan skalabilitas.

Atoms (src/components/common/): Elemen UI dasar dan terkecil yang tidak dapat dipecah lagi (misalnya, Button.vue, Input.vue, Icon.vue). Mereka fokus pada satu tujuan dan tidak memiliki state internal yang kompleks.

Molecules (src/components/ui/): Kelompok atoms yang berfungsi bersama sebagai unit (misalnya, FormGroup.vue yang terdiri dari Input dan Label, Dropdown.vue). Mereka memiliki state sederhana dan logika terkait fungsinya.

Organisms (src/components/kanban/ atau spesifik fitur): Kelompok molecules dan/atau atoms yang membentuk bagian yang lebih kompleks dan fungsional dari UI (misalnya, EmailCard.vue, KanbanColumn.vue, UserAvatar.vue). Mereka mulai memiliki state dan logika yang lebih signifikan.

Templates (src/layouts/): Mengatur organisms ke dalam layout halaman, memberikan struktur konten tanpa styling atau data spesifik (misalnya, DefaultLayout.vue yang memiliki header, sidebar, dan main content area).

Pages/Views (src/views/): Instance spesifik dari templates dengan data nyata yang diambil dari stores atau API. Ini adalah komponen yang di-render oleh Vue Router (misalnya, Dashboard.vue, Settings.vue, Login.vue).

2.3 Modularitas & Integrasi Plugin (Frontend)
Frontend KirimPintar dirancang untuk mengakomodasi ekstensi UI dari plugin pihak ketiga, memungkinkan kustomisasi yang luas tanpa memodifikasi core aplikasi.

Titik Ekstensi UI (Injection Points): Area-area yang telah ditentukan di UI KirimPintar di mana plugin dapat mendaftarkan dan "menyuntikkan" komponen Vue mereka. Ini akan dicapai melalui:

Sistem Slot: Komponen core akan menyediakan slot bernama yang dapat diisi oleh plugin dengan konten atau komponen kustom.

Dynamic Component Loading: Frontend akan memiliki mekanisme untuk secara dinamis memuat komponen Vue dari plugin berdasarkan konfigurasi yang diterima dari backend.

Event Bus/Global State: Sistem event bus internal atau state global (Pinia) dapat digunakan untuk komunikasi antara core aplikasi dan plugin UI.

Pendaftaran Plugin: Plugin backend akan menginformasikan frontend tentang komponen UI yang mereka sediakan, endpoint API khusus plugin, dan metadata lainnya melalui API. Frontend kemudian akan memproses informasi ini untuk merender UI plugin yang relevan.

Komunikasi Plugin-Frontend:

Plugin frontend akan berkomunikasi dengan backend plugin mereka sendiri melalui API khusus yang disediakan oleh backend KirimPintar.

WebSockets dapat digunakan untuk real-time updates dari backend ke frontend untuk plugin yang membutuhkan interaksi instan.

3. Lingkungan Pengembangan Frontend
3.1 Prasyarat
Node.js: Versi LTS terbaru (direkomendasikan).

npm atau Yarn: Package manager untuk Node.js.

Git: Untuk manajemen kode sumber.

Akses ke Backend: Pastikan backend KirimPintar Anda berjalan dan dapat diakses (biasanya melalui Docker Compose di VPS lokal atau remote).

3.2 Pengaturan Lingkungan
Klon Repositori Frontend:

git clone [URL_REPOSITORI_FRONTEND_KIRIMPINTAR]
cd kirimpintar-frontend

Instal Dependensi:

npm install
# atau
yarn install

Konfigurasi Variabel Lingkungan:
Buat file .env di root proyek frontend. Ini akan berisi variabel seperti URL API backend.

VITE_API_BASE_URL=http://localhost:8000/api # Ganti dengan URL API backend Anda

Catatan: VITE_ adalah awalan untuk variabel lingkungan yang diekspos ke kode klien oleh Vite.

Jalankan Server Pengembangan:

npm run dev
# atau
yarn dev

Ini akan memulai server pengembangan Vite, biasanya di http://localhost:5173 (atau port lain yang tersedia). Aplikasi akan otomatis reload saat Anda membuat perubahan kode.

4. Struktur Proyek
Struktur proyek Vue.js akan mengikuti konvensi standar untuk menjaga keteraturan dan skalabilitas.

kirimpintar-frontend/
├── public/                 # Aset statis (misalnya, index.html, favicon)
├── src/
│   ├── assets/             # Aset statis yang diimpor (gambar, font, CSS global)
│   ├── components/         # Komponen Vue yang dapat digunakan kembali
│   │   ├── common/         # Atoms: Komponen dasar/atomik (Button.vue, Input.vue)
│   │   ├── ui/             # Molecules: Komponen UI kompleks (Modal.vue, Dropdown.vue)
│   │   └── kanban/         # Organisms: Komponen spesifik Kanban (EmailCard.vue, KanbanColumn.vue)
│   ├── layouts/            # Templates: Layout aplikasi (misalnya, DefaultLayout.vue)
│   ├── router/             # Konfigurasi Vue Router (index.js)
│   ├── stores/             # Modul Pinia untuk manajemen state (userStore.js, emailStore.js)
│   ├── views/              # Pages/Views: Komponen halaman/view (Dashboard.vue, Settings.vue, Login.vue)
│   ├── services/           # Modul untuk interaksi API (api.js, auth.js, emailService.js)
│   ├── utils/              # Fungsi utilitas (helpers.js, validators.js, dateUtils.js)
│   ├── App.vue             # Komponen root aplikasi
│   └── main.js             # Entry point aplikasi Vue
├── .env                    # Variabel lingkungan
├── .gitignore              # File yang diabaikan oleh Git
├── package.json            # Dependensi proyek dan skrip
├── vite.config.js          # Konfigurasi Vite
└── tailwind.config.js      # Konfigurasi Tailwind CSS

5. Konsep & Pedoman Utama
5.1 Manajemen State (Pinia)
Gunakan Pinia untuk semua manajemen state global.

Setiap modul state (misalnya, userStore, emailStore, columnStore) harus didefinisikan dengan jelas di src/stores/.

Struktur stores harus modular, dengan setiap store bertanggung jawab atas satu bagian state aplikasi (misalnya, userStore menangani data pengguna dan otentikasi; emailStore menangani data email dan operasi terkait).

Hindari mengelola state yang kompleks di tingkat komponen lokal (data()) jika state tersebut perlu diakses atau dimodifikasi oleh banyak komponen.

5.2 Desain Komponen
Single Responsibility Principle (SRP): Setiap komponen harus memiliki satu tanggung jawab utama.

Props Down, Events Up: Komunikasi antar komponen harus melalui props (dari induk ke anak) dan events (dari anak ke induk) untuk menjaga alur data yang jelas.

Slot: Gunakan slot untuk membuat komponen yang lebih fleksibel dan dapat digunakan kembali, memungkinkan injeksi konten dari komponen induk.

Penamaan Komponen: Gunakan PascalCase untuk nama file dan nama komponen (misalnya, EmailCard.vue).

Composition API (Vue 3): Manfaatkan Composition API untuk mengorganisir logika komponen yang kompleks agar lebih reusable, mudah dibaca, dan maintainable melalui fungsi composable.

5.3 Styling (Tailwind CSS)
Gunakan kelas utility Tailwind CSS secara langsung di markup komponen untuk styling yang cepat dan konsisten.

Hindari menulis CSS kustom sebanyak mungkin. Jika perlu, gunakan @apply di dalam blok <style> dengan scoped atau buat komponen baru yang mengkapsulasi styling tersebut.

Pastikan konsistensi desain dengan menggunakan theme Tailwind yang terdefinisi di tailwind.config.js (warna, spacing, font, dll.).

Design System: Pertimbangkan untuk membangun atau mengadopsi design system yang lebih formal seiring pertumbuhan proyek, yang dapat diimplementasikan menggunakan Tailwind dan komponen Vue.

5.4 Interaksi API (Axios)
Semua panggilan API harus dienkapsulasi dalam modul di src/services/. Contoh: emailService.js akan berisi semua fungsi untuk berinteraksi dengan endpoint email API.

Gunakan interceptor Axios untuk menangani:

Otentikasi: Menambahkan token otorisasi pada setiap permintaan.

Penanganan Kesalahan Global: Mencegat respons error (misalnya, 401, 403, 500) dan menampilkan pesan yang relevan atau mengarahkan pengguna ke halaman login.

Loading States: Mengelola loading states global atau per permintaan untuk memberikan umpan balik visual kepada pengguna.

5.5 Penanganan Kesalahan
Implementasikan error boundaries (jika menggunakan Vue 3 dengan errorCaptured hook) di komponen tingkat atas untuk menangkap kesalahan UI yang tidak tertangani dan mencegah aplikasi crash total.

Tangani kesalahan API secara spesifik di src/services/api.js dan berikan umpan balik yang jelas kepada pengguna (misalnya, pesan toast untuk kesalahan validasi, modal untuk kesalahan server).

Gunakan try-catch di dalam fungsi asinkron untuk menangani kesalahan operasional.

5.6 Kode yang Bersih & Terstruktur
Ikuti pedoman linting (ESLint) dan formatting (Prettier) yang akan dikonfigurasi dalam proyek untuk menjaga konsistensi kode.

Tulis kode yang mudah dibaca, dengan komentar yang jelas untuk logika yang kompleks atau bagian yang tidak intuitif.

Lakukan code review secara teratur untuk menjaga kualitas kode.

5.7 Pertimbangan Performa
Lazy Loading (Code Splitting): Gunakan lazy loading untuk komponen halaman (src/views/) dan rute untuk mengurangi ukuran bundle awal dan mempercepat waktu muat aplikasi.

Optimasi Gambar: Gunakan format gambar yang efisien dan optimalkan ukurannya.

Virtual Scrolling: Untuk papan Kanban dengan banyak kartu, pertimbangkan implementasi virtual scrolling untuk mengelola kinerja rendering.

Memoization/Caching: Terapkan memoization atau caching di sisi klien untuk data yang sering diakses tetapi jarang berubah.

5.8 Pertimbangan Keamanan (Frontend)
Cross-Site Scripting (XSS) Prevention: Selalu sanitasi input pengguna sebelum menampilkannya di UI. Vue.js secara default sudah melindungi dari XSS dasar, tetapi tetap waspada terhadap injeksi HTML/JavaScript dari data yang diterima dari backend.

Cross-Site Request Forgery (CSRF) Protection: Pastikan backend menerapkan perlindungan CSRF, dan frontend mengirimkan token CSRF yang diperlukan pada permintaan yang mengubah state (POST, PUT, DELETE).

Secure API Calls: Selalu gunakan HTTPS untuk semua komunikasi API untuk melindungi data dalam perjalanan.

Penyimpanan Token Aman: Simpan token otentikasi (misalnya, JWT) di tempat yang aman (misalnya, HttpOnly cookies atau localStorage dengan mitigasi risiko yang tepat) dan pastikan token memiliki masa berlaku yang terbatas.

6. Integrasi Frontend Plugin (Konsep)
Meskipun detail implementasi akan berkembang, frontend engineer perlu memahami konsep integrasi plugin UI:

Titik Injeksi (Injection Points): Area-area yang telah ditentukan di UI KirimPintar di mana plugin dapat mendaftarkan dan "menyuntikkan" komponen Vue mereka. Ini bisa berupa:

Tombol tambahan di bilah alat.

Panel baru di detail kartu email.

Item menu tambahan di navigasi.

Widget di dashboard.

Pendaftaran Plugin: Plugin backend akan menginformasikan frontend tentang komponen UI yang mereka sediakan melalui API. Frontend kemudian akan secara dinamis memuat dan merender komponen tersebut.

Komunikasi Plugin-UI: Plugin frontend akan berkomunikasi dengan backend plugin mereka sendiri melalui API, atau dengan core KirimPintar API.

7. Deployment Frontend
Frontend akan dibangun menjadi aset statis (HTML, CSS, JavaScript) menggunakan Vite (npm run build atau yarn build).

Aset-aset ini kemudian akan disajikan oleh web server Nginx (yang berjalan dalam container Docker terpisah) di VPS pengguna.

Nginx juga akan berfungsi sebagai reverse proxy untuk mengarahkan permintaan API ke backend Python.

8. Dukungan
Jika Anda memiliki pertanyaan lebih lanjut atau mengalami masalah selama pengembangan frontend, silakan:

Merujuk pada dokumentasi backend untuk detail API.

Mencari di forum komunitas KirimPintar.

Menghubungi tim inti KirimPintar melalui saluran dukungan yang tersedia.

Selamat mengembangkan KirimPintar!