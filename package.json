{"name": "kirim-pintar-vue", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "type-check": "vue-tsc --noEmit", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"lucide-vue-next": "^0.344.0", "pinia": "^2.1.7", "vue": "^3.4.0", "vue-router": "^4.2.5", "vuedraggable": "^4.1.0"}, "devDependencies": {"@types/node": "^20.10.0", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-vue": "^4.5.2", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.5.1", "autoprefixer": "^10.4.18", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.19.2", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "vite": "^5.4.2", "vue-tsc": "^2.0.0"}}