import React, { useState } from 'react';
import Header from './components/Header';
import Sidebar from './components/Sidebar';
import Column from './components/Column';
import Analytics from './components/Analytics';
import TeamManagement from './components/TeamManagement';
import { initialColumns, mockTeam } from './data/mockData';
import { Column as ColumnType, EmailCard } from './types';

function App() {
  const [columns, setColumns] = useState<ColumnType[]>(initialColumns);
  const [activeView, setActiveView] = useState('board');
  const [draggedEmailId, setDraggedEmailId] = useState<string | null>(null);
  const [selectedEmail, setSelectedEmail] = useState<EmailCard | null>(null);

  const handleDragStart = (e: React.DragEvent, emailId: string) => {
    setDraggedEmailId(emailId);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const handleDrop = (e: React.DragEvent, targetColumnId: string) => {
    e.preventDefault();
    
    if (!draggedEmailId) return;

    setColumns(prev => {
      const newColumns = [...prev];
      let draggedEmail: EmailCard | null = null;
      
      // Find and remove the dragged email from its current column
      for (const column of newColumns) {
        const emailIndex = column.cards.findIndex(card => card.id === draggedEmailId);
        if (emailIndex !== -1) {
          draggedEmail = column.cards[emailIndex];
          column.cards.splice(emailIndex, 1);
          break;
        }
      }

      // Add the email to the target column
      if (draggedEmail) {
        const targetColumn = newColumns.find(col => col.id === targetColumnId);
        if (targetColumn) {
          targetColumn.cards.push(draggedEmail);
        }
      }

      return newColumns;
    });

    setDraggedEmailId(null);
  };

  const handleEmailClick = (email: EmailCard) => {
    setSelectedEmail(email);
    // Here you would typically open a modal or detail view
    console.log('Email clicked:', email);
  };

  const handleCreateEmail = () => {
    console.log('Create new email');
    // Here you would open the email composition modal
  };

  const handleAddEmail = (columnId: string) => {
    console.log('Add email to column:', columnId);
    // Here you would open the email creation modal for specific column
  };

  const renderContent = () => {
    switch (activeView) {
      case 'analytics':
        return <Analytics />;
      case 'team':
        return <TeamManagement team={mockTeam} />;
      case 'board':
      default:
        return (
          <div className="flex-1 p-6">
            <div className="mb-6">
              <h1 className="text-2xl font-bold text-gray-900 mb-2">Papan Email</h1>
              <p className="text-gray-600">Kelola semua email pelanggan dengan sistem visual yang mudah</p>
            </div>
            
            {/* AI Status Bar */}
            <div className="bg-gradient-to-r from-blue-50 to-teal-50 border border-blue-200 rounded-lg p-4 mb-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="font-medium text-gray-900">AI Assistant Aktif</span>
                  <span className="text-sm text-gray-600">Memproses 5 email baru | Akurasi 94%</span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <span>12 insights hari ini</span>
                  <span>•</span>
                  <span>2.4 jam waktu hemat</span>
                </div>
              </div>
            </div>

            {/* Kanban Board */}
            <div className="flex space-x-6 overflow-x-auto pb-6">
              {columns.map((column) => (
                <Column
                  key={column.id}
                  column={column}
                  team={mockTeam}
                  onDragOver={handleDragOver}
                  onDrop={handleDrop}
                  onDragStart={handleDragStart}
                  onEmailClick={handleEmailClick}
                  onAddEmail={handleAddEmail}
                />
              ))}
            </div>

            {/* Quick Stats */}
            <div className="mt-8 grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="bg-white rounded-lg border border-gray-200 p-4">
                <h3 className="text-sm font-medium text-gray-600 mb-2">Total Email Hari Ini</h3>
                <p className="text-2xl font-bold text-gray-900">23</p>
                <p className="text-xs text-green-600">+15% dari kemarin</p>
              </div>
              <div className="bg-white rounded-lg border border-gray-200 p-4">
                <h3 className="text-sm font-medium text-gray-600 mb-2">Rata-rata Respon</h3>
                <p className="text-2xl font-bold text-gray-900">1.8 jam</p>
                <p className="text-xs text-green-600">-0.3 jam dari target</p>
              </div>
              <div className="bg-white rounded-lg border border-gray-200 p-4">
                <h3 className="text-sm font-medium text-gray-600 mb-2">Email Resolved</h3>
                <p className="text-2xl font-bold text-gray-900">18</p>
                <p className="text-xs text-blue-600">78% dari total</p>
              </div>
              <div className="bg-white rounded-lg border border-gray-200 p-4">
                <h3 className="text-sm font-medium text-gray-600 mb-2">Kepuasan Pelanggan</h3>
                <p className="text-2xl font-bold text-gray-900">4.8/5</p>
                <p className="text-xs text-green-600">+0.2 dari bulan lalu</p>
              </div>
            </div>
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <Header onCreateEmail={handleCreateEmail} />
      <div className="flex flex-1">
        <Sidebar activeView={activeView} onViewChange={setActiveView} />
        {renderContent()}
      </div>
    </div>
  );
}

export default App;