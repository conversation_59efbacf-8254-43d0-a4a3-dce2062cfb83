<template>
  <div class="min-h-screen bg-gray-50 flex flex-col">
    <Header @create-email="handleCreateEmail" />
    <div class="flex flex-1">
      <Sidebar />
      <router-view />
    </div>
  </div>
</template>

<script setup lang="ts">
import Header from '@/components/Header.vue';
import Sidebar from '@/components/Sidebar.vue';

const handleCreateEmail = () => {
  console.log('Create new email');
  // Here you would open the email composition modal
};
</script>

<style scoped>
/* Component-specific styles can go here */
</style>
