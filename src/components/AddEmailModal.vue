<template>
  <div v-if="isOpen" class="fixed inset-0 z-50 overflow-y-auto">
    <!-- Backdrop -->
    <div 
      class="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
      @click="closeModal"
    ></div>
    
    <!-- Modal -->
    <div class="flex min-h-full items-center justify-center p-4">
      <div class="relative bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden animate-fade-in">
        <!-- Header -->
        <div class="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 class="text-xl font-semibold text-gray-900">Add Email to {{ columnTitle }}</h2>
          <button
            @click="closeModal"
            class="p-2 text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X class="w-5 h-5" />
          </button>
        </div>

        <!-- Input Method Selection -->
        <div class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
            <!-- Sync Integration -->
            <div
              @click="selectMethod('sync')"
              class="p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 hover:shadow-md"
              :class="selectedMethod === 'sync' ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-blue-300'"
            >
              <div class="flex items-center space-x-3 mb-3">
                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <RefreshCw class="w-5 h-5 text-blue-600" />
                </div>
                <div>
                  <h3 class="font-medium text-gray-900">Auto Sync</h3>
                  <p class="text-xs text-gray-500">Gmail, Outlook, etc.</p>
                </div>
              </div>
              <p class="text-sm text-gray-600">Connect your email accounts for automatic synchronization</p>
            </div>

            <!-- Forward Email -->
            <div
              @click="selectMethod('forward')"
              class="p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 hover:shadow-md"
              :class="selectedMethod === 'forward' ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-blue-300'"
            >
              <div class="flex items-center space-x-3 mb-3">
                <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                  <Forward class="w-5 h-5 text-green-600" />
                </div>
                <div>
                  <h3 class="font-medium text-gray-900">Forward Email</h3>
                  <p class="text-xs text-gray-500">Paste email content</p>
                </div>
              </div>
              <p class="text-sm text-gray-600">Forward important emails to your unique address</p>
            </div>

            <!-- Manual Creation -->
            <div
              @click="selectMethod('manual')"
              class="p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 hover:shadow-md"
              :class="selectedMethod === 'manual' ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-blue-300'"
            >
              <div class="flex items-center space-x-3 mb-3">
                <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                  <Edit class="w-5 h-5 text-purple-600" />
                </div>
                <div>
                  <h3 class="font-medium text-gray-900">Create Manual</h3>
                  <p class="text-xs text-gray-500">Phone calls, meetings</p>
                </div>
              </div>
              <p class="text-sm text-gray-600">Create email cards for phone calls or meetings</p>
            </div>

            <!-- Online Form -->
            <div
              @click="selectMethod('form')"
              class="p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 hover:shadow-md"
              :class="selectedMethod === 'form' ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-blue-300'"
            >
              <div class="flex items-center space-x-3 mb-3">
                <div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                  <FileText class="w-5 h-5 text-orange-600" />
                </div>
                <div>
                  <h3 class="font-medium text-gray-900">Online Form</h3>
                  <p class="text-xs text-gray-500">Website integration</p>
                </div>
              </div>
              <p class="text-sm text-gray-600">Create forms for your website to capture leads</p>
            </div>

            <!-- System Integration -->
            <div
              @click="selectMethod('integration')"
              class="p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 hover:shadow-md"
              :class="selectedMethod === 'integration' ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-blue-300'"
            >
              <div class="flex items-center space-x-3 mb-3">
                <div class="w-10 h-10 bg-teal-100 rounded-lg flex items-center justify-center">
                  <Zap class="w-5 h-5 text-teal-600" />
                </div>
                <div>
                  <h3 class="font-medium text-gray-900">System Integration</h3>
                  <p class="text-xs text-gray-500">API, webhooks</p>
                </div>
              </div>
              <p class="text-sm text-gray-600">Connect with your existing systems and tools</p>
            </div>

            <!-- Import Archive -->
            <div
              @click="selectMethod('import')"
              class="p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 hover:shadow-md"
              :class="selectedMethod === 'import' ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-blue-300'"
            >
              <div class="flex items-center space-x-3 mb-3">
                <div class="w-10 h-10 bg-indigo-100 rounded-lg flex items-center justify-center">
                  <Upload class="w-5 h-5 text-indigo-600" />
                </div>
                <div>
                  <h3 class="font-medium text-gray-900">Import Archive</h3>
                  <p class="text-xs text-gray-500">CSV, EML files</p>
                </div>
              </div>
              <p class="text-sm text-gray-600">Import existing email archives and data</p>
            </div>
          </div>

          <!-- Method-specific Content -->
          <div v-if="selectedMethod" class="border-t border-gray-200 pt-6">
            <!-- Sync Integration -->
            <div v-if="selectedMethod === 'sync'" class="space-y-4">
              <h3 class="text-lg font-medium text-gray-900">Email Account Synchronization</h3>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="p-4 border border-gray-200 rounded-lg">
                  <div class="flex items-center space-x-3 mb-3">
                    <img src="https://upload.wikimedia.org/wikipedia/commons/7/7e/Gmail_icon_%282020%29.svg" alt="Gmail" class="w-8 h-8" />
                    <div>
                      <h4 class="font-medium">Gmail</h4>
                      <p class="text-sm text-gray-500">Connect your Gmail account</p>
                    </div>
                  </div>
                  <button class="w-full px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                    Connect Gmail
                  </button>
                </div>
                <div class="p-4 border border-gray-200 rounded-lg">
                  <div class="flex items-center space-x-3 mb-3">
                    <img src="https://upload.wikimedia.org/wikipedia/commons/d/df/Microsoft_Office_Outlook_%282018%E2%80%93present%29.svg" alt="Outlook" class="w-8 h-8" />
                    <div>
                      <h4 class="font-medium">Outlook</h4>
                      <p class="text-sm text-gray-500">Connect your Outlook account</p>
                    </div>
                  </div>
                  <button class="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    Connect Outlook
                  </button>
                </div>
              </div>
            </div>

            <!-- Forward Email -->
            <div v-if="selectedMethod === 'forward'" class="space-y-4">
              <h3 class="text-lg font-medium text-gray-900">Forward Email</h3>
              <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 class="font-medium text-blue-900 mb-2">Your Unique Forward Address:</h4>
                <div class="flex items-center space-x-2">
                  <code class="bg-white px-3 py-2 rounded border text-sm font-mono">
                    {{ forwardAddress }}
                  </code>
                  <button
                    @click="copyForwardAddress"
                    class="px-3 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                  >
                    <Copy class="w-4 h-4" />
                  </button>
                </div>
                <p class="text-sm text-blue-700 mt-2">
                  Forward any email to this address and it will automatically appear in your {{ columnTitle }} column.
                </p>
              </div>
            </div>

            <!-- Manual Creation -->
            <div v-if="selectedMethod === 'manual'" class="space-y-4">
              <h3 class="text-lg font-medium text-gray-900">Create Email Manually</h3>
              <button
                @click="openManualEmailForm"
                class="w-full p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-400 hover:bg-blue-50 transition-all duration-200"
              >
                <Plus class="w-8 h-8 text-gray-400 mx-auto mb-2" />
                <p class="text-gray-600">Click to create a new email card</p>
              </button>
            </div>

            <!-- Online Form -->
            <div v-if="selectedMethod === 'form'" class="space-y-4">
              <h3 class="text-lg font-medium text-gray-900">Online Form Builder</h3>
              <button
                @click="openFormBuilder"
                class="w-full px-4 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
              >
                Create New Form
              </button>
              <div class="text-sm text-gray-600">
                <p>Create custom forms for your website to capture:</p>
                <ul class="list-disc list-inside mt-2 space-y-1">
                  <li>Contact inquiries</li>
                  <li>Support requests</li>
                  <li>Sales leads</li>
                  <li>Feedback submissions</li>
                </ul>
              </div>
            </div>

            <!-- System Integration -->
            <div v-if="selectedMethod === 'integration'" class="space-y-4">
              <h3 class="text-lg font-medium text-gray-900">System Integration</h3>
              <button
                @click="openIntegrationHub"
                class="w-full px-4 py-3 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors"
              >
                Open Integration Hub
              </button>
            </div>

            <!-- Import Archive -->
            <div v-if="selectedMethod === 'import'" class="space-y-4">
              <h3 class="text-lg font-medium text-gray-900">Import Email Archive</h3>
              <button
                @click="openImportDialog"
                class="w-full px-4 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
              >
                Select Files to Import
              </button>
            </div>
          </div>
        </div>

        <!-- Footer -->
        <div class="flex items-center justify-end p-6 border-t border-gray-200 space-x-3">
          <button
            @click="closeModal"
            class="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          <button
            v-if="selectedMethod"
            @click="proceedWithMethod"
            class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Continue
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { X, RefreshCw, Forward, Edit, FileText, Zap, Upload, Plus, Copy } from 'lucide-vue-next';

const props = defineProps<{
  isOpen: boolean;
  columnId: string;
  columnTitle: string;
}>();

const emit = defineEmits<{
  'close': [];
  'create-manual': [columnId: string];
  'open-form-builder': [];
  'open-integration-hub': [];
  'open-import-dialog': [];
}>();

const selectedMethod = ref<string>('');

const forwardAddress = computed(() => {
  return `${props.columnId}.${generateRandomId()}@emailflow.com`;
});

const generateRandomId = () => {
  return Math.random().toString(36).substring(2, 8);
};

const selectMethod = (method: string) => {
  selectedMethod.value = method;
};

const closeModal = () => {
  selectedMethod.value = '';
  emit('close');
};

const copyForwardAddress = async () => {
  try {
    await navigator.clipboard.writeText(forwardAddress.value);
    // Show success message
  } catch (err) {
    console.error('Failed to copy:', err);
  }
};

const openManualEmailForm = () => {
  emit('create-manual', props.columnId);
  closeModal();
};

const openFormBuilder = () => {
  emit('open-form-builder');
  closeModal();
};

const openIntegrationHub = () => {
  emit('open-integration-hub');
  closeModal();
};

const openImportDialog = () => {
  emit('open-import-dialog');
  closeModal();
};

const proceedWithMethod = () => {
  switch (selectedMethod.value) {
    case 'manual':
      openManualEmailForm();
      break;
    case 'form':
      openFormBuilder();
      break;
    case 'integration':
      openIntegrationHub();
      break;
    case 'import':
      openImportDialog();
      break;
    default:
      closeModal();
  }
};
</script>
