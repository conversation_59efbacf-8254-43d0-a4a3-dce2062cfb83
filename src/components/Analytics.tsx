import React from 'react';
import { BarChart3, TrendingUp, Clock, Users, Mail, Zap } from 'lucide-react';

const Analytics: React.FC = () => {
  const stats = [
    {
      label: 'Total Email',
      value: '1,247',
      change: '+12%',
      trend: 'up',
      icon: Mail,
      color: 'text-blue-600'
    },
    {
      label: 'Rata-rata Respon',
      value: '2.4 jam',
      change: '-8%',
      trend: 'down',
      icon: Clock,
      color: 'text-green-600'
    },
    {
      label: 'Tim Aktif',
      value: '12',
      change: '+2',
      trend: 'up',
      icon: Users,
      color: 'text-purple-600'
    },
    {
      label: 'AI Insights',
      value: '89%',
      change: '+5%',
      trend: 'up',
      icon: Zap,
      color: 'text-orange-600'
    }
  ];

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Analytics Dashboard</h1>
        <p className="text-gray-600">Pantau performa tim dan efisiensi email management</p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {stats.map((stat, index) => (
          <div key={index} className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <div className={`p-2 rounded-lg bg-gray-50 ${stat.color}`}>
                <stat.icon className="w-6 h-6" />
              </div>
              <span className={`text-sm font-medium ${
                stat.trend === 'up' ? 'text-green-600' : 'text-red-600'
              }`}>
                {stat.change}
              </span>
            </div>
            <h3 className="text-2xl font-bold text-gray-900 mb-1">{stat.value}</h3>
            <p className="text-sm text-gray-600">{stat.label}</p>
          </div>
        ))}
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Volume Email per Hari</h3>
          <div className="h-64 flex items-end justify-between space-x-2">
            {[45, 32, 68, 55, 43, 67, 52].map((height, index) => (
              <div key={index} className="flex-1 bg-blue-100 rounded-t" style={{ height: `${height}%` }}>
                <div className="w-full bg-blue-500 rounded-t" style={{ height: '30%' }}></div>
              </div>
            ))}
          </div>
          <div className="flex justify-between text-xs text-gray-500 mt-2">
            <span>Sen</span>
            <span>Sel</span>
            <span>Rab</span>
            <span>Kam</span>
            <span>Jum</span>
            <span>Sab</span>
            <span>Min</span>
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Distribusi Prioritas</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Tinggi</span>
              <div className="flex-1 mx-4 bg-gray-200 rounded-full h-2">
                <div className="bg-red-500 h-2 rounded-full" style={{ width: '35%' }}></div>
              </div>
              <span className="text-sm font-medium">35%</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Sedang</span>
              <div className="flex-1 mx-4 bg-gray-200 rounded-full h-2">
                <div className="bg-yellow-500 h-2 rounded-full" style={{ width: '45%' }}></div>
              </div>
              <span className="text-sm font-medium">45%</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Rendah</span>
              <div className="flex-1 mx-4 bg-gray-200 rounded-full h-2">
                <div className="bg-green-500 h-2 rounded-full" style={{ width: '20%' }}></div>
              </div>
              <span className="text-sm font-medium">20%</span>
            </div>
          </div>
        </div>
      </div>

      {/* AI Performance */}
      <div className="mt-6 bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Performa AI</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <BarChart3 className="w-8 h-8 text-blue-600" />
            </div>
            <h4 className="font-semibold text-gray-900">Akurasi Kategorisasi</h4>
            <p className="text-2xl font-bold text-blue-600">94%</p>
            <p className="text-sm text-gray-600">+3% dari bulan lalu</p>
          </div>
          <div className="text-center">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <TrendingUp className="w-8 h-8 text-green-600" />
            </div>
            <h4 className="font-semibold text-gray-900">Prediksi Prioritas</h4>
            <p className="text-2xl font-bold text-green-600">91%</p>
            <p className="text-sm text-gray-600">+1% dari bulan lalu</p>
          </div>
          <div className="text-center">
            <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <Zap className="w-8 h-8 text-purple-600" />
            </div>
            <h4 className="font-semibold text-gray-900">Waktu Hemat</h4>
            <p className="text-2xl font-bold text-purple-600">4.2 jam</p>
            <p className="text-sm text-gray-600">Per hari per agent</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Analytics;