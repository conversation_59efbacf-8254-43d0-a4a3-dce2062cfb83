<template>
  <div class="p-6">
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-900 mb-2">Analytics Dashboard</h1>
      <p class="text-gray-600">Pantau performa tim dan efisiensi email management</p>
    </div>

    <!-- Stats Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <div
        v-for="(stat, index) in stats"
        :key="index"
        class="bg-white rounded-lg border border-gray-200 p-6"
      >
        <div class="flex items-center justify-between mb-4">
          <div :class="`p-2 rounded-lg bg-gray-50 ${stat.color}`">
            <component :is="stat.icon" class="w-6 h-6" />
          </div>
          <span
            :class="`text-sm font-medium ${
              stat.trend === 'up' ? 'text-green-600' : 'text-red-600'
            }`"
          >
            {{ stat.change }}
          </span>
        </div>
        <h3 class="text-2xl font-bold text-gray-900 mb-1">{{ stat.value }}</h3>
        <p class="text-sm text-gray-600">{{ stat.label }}</p>
      </div>
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <div class="bg-white rounded-lg border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Volume Email per Hari</h3>
        <div class="h-64 flex items-end justify-between space-x-2">
          <div
            v-for="(height, index) in chartData"
            :key="index"
            class="flex-1 bg-blue-100 rounded-t"
            :style="{ height: `${height}%` }"
          >
            <div class="w-full bg-blue-500 rounded-t" style="height: 30%"></div>
          </div>
        </div>
        <div class="flex justify-between text-xs text-gray-500 mt-2">
          <span>Sen</span>
          <span>Sel</span>
          <span>Rab</span>
          <span>Kam</span>
          <span>Jum</span>
          <span>Sab</span>
          <span>Min</span>
        </div>
      </div>

      <div class="bg-white rounded-lg border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Distribusi Prioritas</h3>
        <div class="space-y-4">
          <div
            v-for="priority in priorityData"
            :key="priority.label"
            class="flex items-center justify-between"
          >
            <span class="text-sm text-gray-600">{{ priority.label }}</span>
            <div class="flex-1 mx-4 bg-gray-200 rounded-full h-2">
              <div
                :class="`h-2 rounded-full ${priority.color}`"
                :style="{ width: `${priority.percentage}%` }"
              ></div>
            </div>
            <span class="text-sm font-medium">{{ priority.percentage }}%</span>
          </div>
        </div>
      </div>
    </div>

    <!-- AI Performance -->
    <div class="mt-6 bg-white rounded-lg border border-gray-200 p-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">Performa AI</h3>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div
          v-for="performance in aiPerformance"
          :key="performance.title"
          class="text-center"
        >
          <div :class="`w-16 h-16 ${performance.bgColor} rounded-full flex items-center justify-center mx-auto mb-3`">
            <component :is="performance.icon" :class="`w-8 h-8 ${performance.iconColor}`" />
          </div>
          <h4 class="font-semibold text-gray-900">{{ performance.title }}</h4>
          <p :class="`text-2xl font-bold ${performance.valueColor}`">{{ performance.value }}</p>
          <p class="text-sm text-gray-600">{{ performance.description }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { BarChart3, TrendingUp, Clock, Users, Mail, Zap } from 'lucide-vue-next';

const stats = [
  {
    label: 'Total Email',
    value: '1,247',
    change: '+12%',
    trend: 'up',
    icon: Mail,
    color: 'text-blue-600'
  },
  {
    label: 'Rata-rata Respon',
    value: '2.4 jam',
    change: '-8%',
    trend: 'down',
    icon: Clock,
    color: 'text-green-600'
  },
  {
    label: 'Tim Aktif',
    value: '12',
    change: '+2',
    trend: 'up',
    icon: Users,
    color: 'text-purple-600'
  },
  {
    label: 'AI Insights',
    value: '89%',
    change: '+5%',
    trend: 'up',
    icon: Zap,
    color: 'text-orange-600'
  }
];

const chartData = [45, 32, 68, 55, 43, 67, 52];

const priorityData = [
  { label: 'Tinggi', percentage: 35, color: 'bg-red-500' },
  { label: 'Sedang', percentage: 45, color: 'bg-yellow-500' },
  { label: 'Rendah', percentage: 20, color: 'bg-green-500' }
];

const aiPerformance = [
  {
    title: 'Akurasi Kategorisasi',
    value: '94%',
    description: '+3% dari bulan lalu',
    icon: BarChart3,
    bgColor: 'bg-blue-100',
    iconColor: 'text-blue-600',
    valueColor: 'text-blue-600'
  },
  {
    title: 'Prediksi Prioritas',
    value: '91%',
    description: '+1% dari bulan lalu',
    icon: TrendingUp,
    bgColor: 'bg-green-100',
    iconColor: 'text-green-600',
    valueColor: 'text-green-600'
  },
  {
    title: 'Waktu Hemat',
    value: '4.2 jam',
    description: 'Per hari per agent',
    icon: Zap,
    bgColor: 'bg-purple-100',
    iconColor: 'text-purple-600',
    valueColor: 'text-purple-600'
  }
];
</script>
