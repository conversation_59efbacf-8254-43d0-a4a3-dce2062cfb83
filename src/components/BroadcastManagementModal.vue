<template>
  <div
    v-if="isOpen"
    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
    @click.self="closeModal"
  >
    <div class="bg-white rounded-lg shadow-xl w-full max-w-5xl mx-4 max-h-[90vh] overflow-hidden">
      <!-- Header -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200">
        <div class="flex items-center space-x-3">
          <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
            <Megaphone class="w-4 h-4 text-orange-600" />
          </div>
          <div>
            <h2 class="text-lg font-semibold text-gray-900">{{ broadcast?.name || 'Broadcast Management' }}</h2>
            <p class="text-sm text-gray-500">{{ columnTitle }} - Manage email broadcast</p>
          </div>
        </div>
        <button
          @click="closeModal"
          class="p-2 hover:bg-gray-100 rounded-lg transition-colors"
        >
          <X class="w-5 h-5 text-gray-400" />
        </button>
      </div>

      <!-- Tab Navigation -->
      <div class="border-b border-gray-200">
        <nav class="flex space-x-8 px-6">
          <button
            v-for="tab in tabs"
            :key="tab.id"
            @click="activeTab = tab.id"
            :class="[
              'py-4 px-1 border-b-2 font-medium text-sm transition-colors',
              activeTab === tab.id
                ? 'border-orange-500 text-orange-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            ]"
          >
            <div class="flex items-center space-x-2">
              <component :is="tab.icon" class="w-4 h-4" />
              <span>{{ tab.name }}</span>
            </div>
          </button>
        </nav>
      </div>

      <!-- Content -->
      <div class="p-6 max-h-[calc(90vh-200px)] overflow-y-auto">
        <!-- Overview Tab -->
        <div v-if="activeTab === 'overview'">
          <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Broadcast Info -->
            <div class="lg:col-span-2">
              <div class="bg-white border border-gray-200 rounded-lg p-6 mb-6">
                <div class="flex items-center justify-between mb-4">
                  <h3 class="text-lg font-medium text-gray-900">Broadcast Details</h3>
                  <span :class="[
                    'px-3 py-1 text-sm rounded-full',
                    broadcast?.status === 'sent' ? 'bg-green-100 text-green-700' :
                    broadcast?.status === 'scheduled' ? 'bg-blue-100 text-blue-700' :
                    broadcast?.status === 'draft' ? 'bg-gray-100 text-gray-700' :
                    'bg-yellow-100 text-yellow-700'
                  ]">
                    {{ broadcast?.status }}
                  </span>
                </div>
                
                <div class="space-y-4">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Campaign Name</label>
                    <p class="text-gray-900">{{ broadcast?.name }}</p>
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Subject Line</label>
                    <p class="text-gray-900">{{ broadcast?.subject }}</p>
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">From Email</label>
                    <p class="text-gray-600">{{ broadcast?.fromEmail || '<EMAIL>' }}</p>
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Send Date</label>
                    <p class="text-gray-900">{{ formatDate(broadcast?.date) }}</p>
                  </div>
                </div>
              </div>

              <!-- Email Content Preview -->
              <div class="bg-white border border-gray-200 rounded-lg p-6">
                <div class="flex items-center justify-between mb-4">
                  <h3 class="text-lg font-medium text-gray-900">Email Content</h3>
                  <button
                    v-if="broadcast?.status === 'draft'"
                    @click="editBroadcast"
                    class="px-3 py-2 text-sm bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
                  >
                    Edit Content
                  </button>
                </div>
                
                <div class="border border-gray-200 rounded-lg p-4 bg-gray-50">
                  <div class="mb-3">
                    <p class="text-sm font-medium text-gray-700">Subject:</p>
                    <p class="text-gray-900">{{ broadcast?.subject }}</p>
                  </div>
                  <div>
                    <p class="text-sm font-medium text-gray-700 mb-2">Content:</p>
                    <div class="text-gray-900 whitespace-pre-wrap">{{ broadcast?.content?.substring(0, 300) }}{{ (broadcast?.content?.length || 0) > 300 ? '...' : '' }}</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Stats Sidebar -->
            <div class="space-y-6">
              <div class="bg-white border border-gray-200 rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Performance</h3>
                <div class="space-y-4">
                  <div class="text-center">
                    <p class="text-2xl font-bold text-gray-900">{{ broadcast?.recipients || 0 }}</p>
                    <p class="text-sm text-gray-500">Recipients</p>
                  </div>
                  <div class="text-center">
                    <p class="text-2xl font-bold text-green-600">{{ broadcast?.opened || 0 }}</p>
                    <p class="text-sm text-gray-500">Opened</p>
                  </div>
                  <div class="text-center">
                    <p class="text-2xl font-bold text-blue-600">{{ broadcast?.clicked || 0 }}</p>
                    <p class="text-sm text-gray-500">Clicked</p>
                  </div>
                  <div class="text-center">
                    <p class="text-2xl font-bold text-orange-600">{{ broadcast?.openRate || 0 }}%</p>
                    <p class="text-sm text-gray-500">Open Rate</p>
                  </div>
                </div>
              </div>

              <div class="bg-white border border-gray-200 rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
                <div class="space-y-3">
                  <button
                    v-if="broadcast?.status === 'draft'"
                    @click="sendBroadcast"
                    class="w-full px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
                  >
                    Send Now
                  </button>
                  <button
                    v-if="broadcast?.status === 'draft'"
                    @click="scheduleBroadcast"
                    class="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Schedule
                  </button>
                  <button
                    @click="duplicateBroadcast"
                    class="w-full px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    Duplicate
                  </button>
                  <button
                    @click="exportBroadcastData"
                    class="w-full px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    Export Data
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Recipients Tab -->
        <div v-if="activeTab === 'recipients'">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-medium text-gray-900">Broadcast Recipients</h3>
            <div class="flex items-center space-x-3">
              <div class="relative">
                <Search class="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  v-model="recipientSearch"
                  type="text"
                  placeholder="Search recipients..."
                  class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                />
              </div>
              <select
                v-model="statusFilter"
                class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              >
                <option value="">All Status</option>
                <option value="sent">Sent</option>
                <option value="opened">Opened</option>
                <option value="clicked">Clicked</option>
                <option value="bounced">Bounced</option>
              </select>
            </div>
          </div>

          <!-- Recipients Table -->
          <div class="bg-white border border-gray-200 rounded-lg overflow-hidden">
            <div class="grid grid-cols-12 gap-4 p-4 bg-gray-50 border-b border-gray-200 text-sm font-medium text-gray-700">
              <div class="col-span-3">Recipient</div>
              <div class="col-span-2">Status</div>
              <div class="col-span-2">Opened</div>
              <div class="col-span-2">Clicked</div>
              <div class="col-span-2">Sent Date</div>
              <div class="col-span-1">Actions</div>
            </div>
            
            <div
              v-for="recipient in filteredRecipients"
              :key="recipient.id"
              class="grid grid-cols-12 gap-4 p-4 border-b border-gray-100 hover:bg-gray-50 transition-colors"
            >
              <div class="col-span-3">
                <div class="flex items-center space-x-3">
                  <div class="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                    <User class="w-4 h-4 text-orange-600" />
                  </div>
                  <div>
                    <p class="font-medium text-gray-900">{{ recipient.name }}</p>
                    <p class="text-sm text-gray-500">{{ recipient.email }}</p>
                  </div>
                </div>
              </div>
              <div class="col-span-2">
                <span :class="[
                  'px-2 py-1 text-xs rounded-full',
                  recipient.status === 'clicked' ? 'bg-green-100 text-green-700' :
                  recipient.status === 'opened' ? 'bg-blue-100 text-blue-700' :
                  recipient.status === 'sent' ? 'bg-gray-100 text-gray-700' :
                  'bg-red-100 text-red-700'
                ]">
                  {{ recipient.status }}
                </span>
              </div>
              <div class="col-span-2 text-sm text-gray-500">
                {{ recipient.openedAt ? formatDate(recipient.openedAt) : '-' }}
              </div>
              <div class="col-span-2 text-sm text-gray-500">
                {{ recipient.clickedAt ? formatDate(recipient.clickedAt) : '-' }}
              </div>
              <div class="col-span-2 text-sm text-gray-500">{{ formatDate(recipient.sentAt) }}</div>
              <div class="col-span-1">
                <button
                  @click="viewRecipientDetails(recipient)"
                  class="text-orange-600 hover:text-orange-700"
                >
                  <Eye class="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Analytics Tab -->
        <div v-if="activeTab === 'analytics'">
          <h3 class="text-lg font-medium text-gray-900 mb-6">Broadcast Analytics</h3>
          
          <!-- Performance Metrics -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white border border-gray-200 rounded-lg p-6">
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-sm font-medium text-gray-600">Delivery Rate</p>
                  <p class="text-2xl font-bold text-gray-900">{{ analytics.deliveryRate }}%</p>
                </div>
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <CheckCircle class="w-6 h-6 text-green-600" />
                </div>
              </div>
              <p class="text-sm text-gray-500 mt-2">{{ analytics.delivered }}/{{ broadcast?.recipients }} delivered</p>
            </div>
            
            <div class="bg-white border border-gray-200 rounded-lg p-6">
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-sm font-medium text-gray-600">Open Rate</p>
                  <p class="text-2xl font-bold text-gray-900">{{ broadcast?.openRate }}%</p>
                </div>
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Mail class="w-6 h-6 text-blue-600" />
                </div>
              </div>
              <p class="text-sm text-gray-500 mt-2">{{ broadcast?.opened }} opens</p>
            </div>
            
            <div class="bg-white border border-gray-200 rounded-lg p-6">
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-sm font-medium text-gray-600">Click Rate</p>
                  <p class="text-2xl font-bold text-gray-900">{{ analytics.clickRate }}%</p>
                </div>
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                  <MousePointer class="w-6 h-6 text-purple-600" />
                </div>
              </div>
              <p class="text-sm text-gray-500 mt-2">{{ broadcast?.clicked }} clicks</p>
            </div>
            
            <div class="bg-white border border-gray-200 rounded-lg p-6">
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-sm font-medium text-gray-600">Bounce Rate</p>
                  <p class="text-2xl font-bold text-gray-900">{{ analytics.bounceRate }}%</p>
                </div>
                <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                  <AlertTriangle class="w-6 h-6 text-red-600" />
                </div>
              </div>
              <p class="text-sm text-gray-500 mt-2">{{ analytics.bounced }} bounced</p>
            </div>
          </div>
          
          <!-- Charts -->
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div class="bg-white border border-gray-200 rounded-lg p-6">
              <h4 class="font-medium text-gray-900 mb-4">Engagement Timeline</h4>
              <div class="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
                <p class="text-gray-500">Chart: Opens and clicks over time</p>
              </div>
            </div>
            
            <div class="bg-white border border-gray-200 rounded-lg p-6">
              <h4 class="font-medium text-gray-900 mb-4">Device & Client Breakdown</h4>
              <div class="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
                <p class="text-gray-500">Chart: Email client and device statistics</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { 
  Megaphone, X, Search, User, Eye, Mail, MousePointer, 
  CheckCircle, AlertTriangle, BarChart3, Users, FileText 
} from 'lucide-vue-next';

interface Props {
  isOpen: boolean;
  broadcast: any;
  columnTitle: string;
}

interface Emits {
  (e: 'close'): void;
  (e: 'edit-broadcast'): void;
  (e: 'send-broadcast'): void;
  (e: 'schedule-broadcast'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// State
const activeTab = ref('overview');
const recipientSearch = ref('');
const statusFilter = ref('');

// Tabs
const tabs = ref([
  { id: 'overview', name: 'Overview', icon: BarChart3 },
  { id: 'recipients', name: 'Recipients', icon: Users },
  { id: 'analytics', name: 'Analytics', icon: BarChart3 }
]);

// Mock data
const recipients = ref([
  {
    id: '1',
    name: 'John Doe',
    email: '<EMAIL>',
    status: 'clicked',
    sentAt: '2024-01-10T09:00:00Z',
    openedAt: '2024-01-10T09:15:00Z',
    clickedAt: '2024-01-10T09:20:00Z'
  },
  {
    id: '2',
    name: 'Jane Smith',
    email: '<EMAIL>',
    status: 'opened',
    sentAt: '2024-01-10T09:00:00Z',
    openedAt: '2024-01-10T10:30:00Z',
    clickedAt: null
  }
]);

const analytics = ref({
  deliveryRate: 98,
  delivered: 153,
  clickRate: 15,
  bounceRate: 2,
  bounced: 3
});

// Computed
const filteredRecipients = computed(() => {
  let filtered = recipients.value;
  
  if (recipientSearch.value) {
    const search = recipientSearch.value.toLowerCase();
    filtered = filtered.filter(recipient =>
      recipient.name.toLowerCase().includes(search) ||
      recipient.email.toLowerCase().includes(search)
    );
  }
  
  if (statusFilter.value) {
    filtered = filtered.filter(recipient => recipient.status === statusFilter.value);
  }
  
  return filtered;
});

// Methods
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString();
};

const editBroadcast = () => {
  emit('edit-broadcast');
};

const sendBroadcast = () => {
  emit('send-broadcast');
};

const scheduleBroadcast = () => {
  emit('schedule-broadcast');
};

const duplicateBroadcast = () => {
  console.log('Duplicate broadcast:', props.broadcast?.id);
};

const exportBroadcastData = () => {
  console.log('Export broadcast data:', props.broadcast?.id);
};

const viewRecipientDetails = (recipient: any) => {
  console.log('View recipient details:', recipient);
};

const closeModal = () => {
  emit('close');
};
</script>
