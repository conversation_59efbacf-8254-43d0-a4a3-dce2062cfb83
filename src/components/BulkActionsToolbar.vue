<template>
  <div
    v-if="selectedCount > 0"
    class="fixed bottom-6 left-1/2 transform -translate-x-1/2 bg-white rounded-lg shadow-lg border border-gray-200 p-4 z-40 animate-slide-up"
  >
    <div class="flex items-center space-x-4">
      <!-- Selection Info -->
      <div class="flex items-center space-x-2">
        <div class="w-6 h-6 bg-blue-100 rounded flex items-center justify-center">
          <span class="text-xs font-medium text-blue-600">{{ selectedCount }}</span>
        </div>
        <span class="text-sm font-medium text-gray-900">
          {{ selectedCount }} email{{ selectedCount > 1 ? 's' : '' }} selected
        </span>
      </div>

      <!-- Divider -->
      <div class="w-px h-6 bg-gray-300"></div>

      <!-- Actions -->
      <div class="flex items-center space-x-2">
        <!-- Move to Column -->
        <div class="relative">
          <button
            @click="showMoveDropdown = !showMoveDropdown"
            class="flex items-center space-x-1 px-3 py-1.5 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
          >
            <Move class="w-4 h-4" />
            <span>Move to</span>
            <ChevronDown class="w-3 h-3" />
          </button>
          
          <!-- Move Dropdown -->
          <div
            v-if="showMoveDropdown"
            class="absolute bottom-full mb-2 left-0 bg-white rounded-lg shadow-lg border border-gray-200 py-1 min-w-32 z-50"
          >
            <button
              v-for="column in availableColumns"
              :key="column.id"
              @click="moveSelectedEmails(column.id)"
              class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
            >
              {{ column.title }}
            </button>
          </div>
        </div>

        <!-- Assign -->
        <div class="relative">
          <button
            @click="showAssignDropdown = !showAssignDropdown"
            class="flex items-center space-x-1 px-3 py-1.5 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
          >
            <UserPlus class="w-4 h-4" />
            <span>Assign</span>
            <ChevronDown class="w-3 h-3" />
          </button>
          
          <!-- Assign Dropdown -->
          <div
            v-if="showAssignDropdown"
            class="absolute bottom-full mb-2 left-0 bg-white rounded-lg shadow-lg border border-gray-200 py-1 min-w-40 z-50"
          >
            <button
              @click="assignSelectedEmails('')"
              class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
            >
              Unassign
            </button>
            <button
              v-for="member in team"
              :key="member.id"
              @click="assignSelectedEmails(member.id)"
              class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors flex items-center space-x-2"
            >
              <img :src="member.avatar" :alt="member.name" class="w-5 h-5 rounded-full" />
              <span>{{ member.name }}</span>
            </button>
          </div>
        </div>

        <!-- Priority -->
        <div class="relative">
          <button
            @click="showPriorityDropdown = !showPriorityDropdown"
            class="flex items-center space-x-1 px-3 py-1.5 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
          >
            <Flag class="w-4 h-4" />
            <span>Priority</span>
            <ChevronDown class="w-3 h-3" />
          </button>
          
          <!-- Priority Dropdown -->
          <div
            v-if="showPriorityDropdown"
            class="absolute bottom-full mb-2 left-0 bg-white rounded-lg shadow-lg border border-gray-200 py-1 min-w-32 z-50"
          >
            <button
              @click="setPriorityForSelected('high')"
              class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors flex items-center space-x-2"
            >
              <div class="w-2 h-2 bg-red-500 rounded-full"></div>
              <span>High</span>
            </button>
            <button
              @click="setPriorityForSelected('medium')"
              class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors flex items-center space-x-2"
            >
              <div class="w-2 h-2 bg-yellow-500 rounded-full"></div>
              <span>Medium</span>
            </button>
            <button
              @click="setPriorityForSelected('low')"
              class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors flex items-center space-x-2"
            >
              <div class="w-2 h-2 bg-green-500 rounded-full"></div>
              <span>Low</span>
            </button>
          </div>
        </div>

        <!-- Delete -->
        <button
          @click="deleteSelectedEmails"
          class="flex items-center space-x-1 px-3 py-1.5 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700 transition-colors"
        >
          <Trash2 class="w-4 h-4" />
          <span>Delete</span>
        </button>
      </div>

      <!-- Divider -->
      <div class="w-px h-6 bg-gray-300"></div>

      <!-- Clear Selection -->
      <button
        @click="clearSelection"
        class="flex items-center space-x-1 px-3 py-1.5 text-sm font-medium text-gray-500 hover:text-gray-700 transition-colors"
      >
        <X class="w-4 h-4" />
        <span>Clear</span>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { Move, UserPlus, Flag, Trash2, X, ChevronDown } from 'lucide-vue-next';
import { useBoardStore } from '@/stores/board';
import { mockTeam } from '@/data/mockData';

const boardStore = useBoardStore();
const team = mockTeam;

const showMoveDropdown = ref(false);
const showAssignDropdown = ref(false);
const showPriorityDropdown = ref(false);

const selectedCount = computed(() => boardStore.selectedEmails.size);
const availableColumns = computed(() => boardStore.columns);

const moveSelectedEmails = (columnId: string) => {
  const selectedIds = Array.from(boardStore.selectedEmails);
  boardStore.bulkMoveEmails(selectedIds, columnId);
  showMoveDropdown.value = false;
};

const assignSelectedEmails = (assigneeId: string) => {
  const selectedIds = Array.from(boardStore.selectedEmails);
  boardStore.bulkAssignEmails(selectedIds, assigneeId);
  showAssignDropdown.value = false;
};

const setPriorityForSelected = (priority: 'high' | 'medium' | 'low') => {
  const selectedIds = Array.from(boardStore.selectedEmails);
  for (const emailId of selectedIds) {
    boardStore.updateEmail(emailId, { priority });
  }
  boardStore.clearSelection();
  showPriorityDropdown.value = false;
};

const deleteSelectedEmails = () => {
  if (confirm(`Are you sure you want to delete ${selectedCount.value} email(s)?`)) {
    const selectedIds = Array.from(boardStore.selectedEmails);
    boardStore.bulkDeleteEmails(selectedIds);
  }
};

const clearSelection = () => {
  boardStore.clearSelection();
};

// Close dropdowns when clicking outside
const closeDropdowns = () => {
  showMoveDropdown.value = false;
  showAssignDropdown.value = false;
  showPriorityDropdown.value = false;
};

onMounted(() => {
  document.addEventListener('click', closeDropdowns);
});

onUnmounted(() => {
  document.removeEventListener('click', closeDropdowns);
});
</script>

<style scoped>
@keyframes slide-up {
  from {
    opacity: 0;
    transform: translate(-50%, 20px);
  }
  to {
    opacity: 1;
    transform: translate(-50%, 0);
  }
}

.animate-slide-up {
  animation: slide-up 0.3s ease-out;
}
</style>
