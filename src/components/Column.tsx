import React from 'react';
import { Plus, <PERSON>ting<PERSON>, Zap } from 'lucide-react';
import { Column as ColumnType, EmailCard as EmailCardType, Team } from '../types';
import EmailCard from './EmailCard';

interface ColumnProps {
  column: ColumnType;
  team: Team[];
  onDragOver: (e: React.DragEvent) => void;
  onDrop: (e: React.DragEvent, columnId: string) => void;
  onDragStart: (e: React.DragEvent, emailId: string) => void;
  onEmailClick: (email: EmailCardType) => void;
  onAddEmail: (columnId: string) => void;
}

const Column: React.FC<ColumnProps> = ({
  column,
  team,
  onDragOver,
  onDrop,
  onDragStart,
  onEmailClick,
  onAddEmail
}) => {
  return (
    <div className="flex-shrink-0 w-80">
      <div className={`rounded-lg border-2 border-dashed ${column.color} min-h-96 p-4`}>
        {/* <PERSON>umn Header */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <h2 className="font-semibold text-gray-800">{column.title}</h2>
            <span className="bg-gray-200 text-gray-700 text-xs px-2 py-1 rounded-full">
              {column.cards.length}
            </span>
            {column.aiEnabled && (
              <Zap className="w-4 h-4 text-blue-500" title="AI Aktif" />
            )}
          </div>
          <div className="flex items-center space-x-1">
            <button
              onClick={() => onAddEmail(column.id)}
              className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <Plus className="w-4 h-4" />
            </button>
            <button className="p-1 text-gray-400 hover:text-gray-600 transition-colors">
              <Settings className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Drop Zone */}
        <div
          onDragOver={onDragOver}
          onDrop={(e) => onDrop(e, column.id)}
          className="min-h-80"
        >
          {column.cards.map((email) => (
            <EmailCard
              key={email.id}
              email={email}
              team={team}
              onDragStart={onDragStart}
              onClick={onEmailClick}
            />
          ))}
          
          {column.cards.length === 0 && (
            <div className="text-center py-12 text-gray-400">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <Plus className="w-8 h-8" />
              </div>
              <p className="text-sm">Tidak ada email</p>
              <p className="text-xs mt-1">Seret email ke sini atau klik + untuk menambah</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Column;