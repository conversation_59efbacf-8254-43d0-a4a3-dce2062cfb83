<template>
  <div class="flex-shrink-0 w-80">
    <div :class="`rounded-lg border-2 border-dashed ${column.color} min-h-96 p-4 bg-white transition-all duration-200`">
      <!-- Column Header -->
      <div class="flex items-center justify-between mb-4">
        <div class="flex items-center space-x-2">
          <h2 class="font-semibold text-gray-800">{{ column.title }}</h2>
          <span class="bg-gray-200 text-gray-700 text-xs px-2 py-1 rounded-full">
            {{ filteredCards.length }}
          </span>
          <Zap v-if="column.aiEnabled" class="w-4 h-4 text-blue-500" title="AI Active" />
        </div>
        <div class="flex items-center space-x-1">
          <button
            @click="showColumnMenu = !showColumnMenu"
            class="p-1 text-gray-400 hover:text-gray-600 transition-colors relative"
          >
            <MoreVertical class="w-4 h-4" />

            <!-- Column Menu -->
            <div
              v-if="showColumnMenu"
              class="absolute right-0 top-6 bg-white rounded-lg shadow-lg border border-gray-200 py-1 min-w-48 z-20"
              @click.stop
            >
              <button
                @click="addNewEmail"
                class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors flex items-center space-x-2"
              >
                <Plus class="w-4 h-4" />
                <span>Add Email</span>
              </button>
              <div class="border-t border-gray-200 my-1"></div>
              <button
                @click="openEmailActions"
                class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors flex items-center space-x-2"
              >
                <Zap class="w-4 h-4" />
                <span>Email Actions</span>
              </button>
              <button
                @click="openSequenceBuilder"
                class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors flex items-center space-x-2"
              >
                <Workflow class="w-4 h-4" />
                <span>Create Sequence</span>
              </button>
              <button
                @click="openBroadcastBuilder"
                class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors flex items-center space-x-2"
              >
                <Megaphone class="w-4 h-4" />
                <span>Send Broadcast</span>
              </button>
              <button
                @click="openTemplateBuilder"
                class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors flex items-center space-x-2"
              >
                <FileText class="w-4 h-4" />
                <span>Create Template</span>
              </button>
              <div class="border-t border-gray-200 my-1"></div>
              <button
                @click="selectAllInColumn"
                class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors flex items-center space-x-2"
              >
                <CheckSquare class="w-4 h-4" />
                <span>Select All</span>
              </button>
              <button
                @click="clearColumnSelection"
                class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors flex items-center space-x-2"
              >
                <Square class="w-4 h-4" />
                <span>Clear Selection</span>
              </button>
              <div class="border-t border-gray-200 my-1"></div>
              <button
                @click="viewColumnAnalytics"
                class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors flex items-center space-x-2"
              >
                <BarChart3 class="w-4 h-4" />
                <span>View Analytics</span>
              </button>
              <button
                @click="exportColumnData"
                class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors flex items-center space-x-2"
              >
                <Download class="w-4 h-4" />
                <span>Export Data</span>
              </button>
              <div class="border-t border-gray-200 my-1"></div>
              <button
                @click="editColumn"
                class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors flex items-center space-x-2"
              >
                <Edit class="w-4 h-4" />
                <span>Edit Column</span>
              </button>
            </div>
          </button>
        </div>
      </div>

      <!-- Drop Zone -->
      <div
        @dragover="handleDragOver"
        @drop="handleDrop"
        @dragenter="handleDragEnter"
        @dragleave="handleDragLeave"
        class="min-h-80 transition-all duration-200 rounded-lg"
        :class="{
          'bg-blue-50 border-2 border-dashed border-blue-300': isDragOver,
          'bg-gray-50': isDragOver
        }"
      >
        <EmailCard
          v-for="email in filteredCards"
          :key="email.id"
          :email="email"
          @drag-start="handleEmailDragStart"
          @click="handleEmailClick"
          @right-click="handleEmailRightClick"
        />

        <div v-if="filteredCards.length === 0" class="text-center py-12 text-gray-400">
          <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
            <Plus class="w-8 h-8" />
          </div>
          <p class="text-sm">No emails</p>
          <p class="text-xs mt-1">Drag emails here or click + to add</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { Plus, Settings, Zap, MoreVertical, CheckSquare, Square, Edit, Workflow, Megaphone, FileText, BarChart3, Download } from 'lucide-vue-next';
import type { Column as ColumnType, EmailCard as EmailCardType } from '@/types';
import EmailCard from './EmailCard.vue';
import { useBoardStore } from '@/stores/board';

const props = defineProps<{
  column: ColumnType;
}>();

const emit = defineEmits<{
  'email-click': [email: EmailCardType];
  'email-right-click': [email: EmailCardType, event: MouseEvent];
  'add-email': [columnId: string];
  'edit-column': [columnId: string];
  'open-email-actions': [];
  'open-sequence-builder': [];
  'open-broadcast-builder': [];
  'open-template-builder': [];
  'view-column-analytics': [columnId: string];
  'export-column-data': [columnId: string];
}>();

const boardStore = useBoardStore();
const showColumnMenu = ref(false);
const isDragOver = ref(false);
const dragCounter = ref(0);

// Filter cards based on current filters
const filteredCards = computed(() => {
  let cards = props.column.cards;

  // Apply search filter
  if (boardStore.filters.search) {
    const search = boardStore.filters.search.toLowerCase();
    cards = cards.filter(card =>
      card.subject.toLowerCase().includes(search) ||
      card.from.toLowerCase().includes(search) ||
      card.content.toLowerCase().includes(search)
    );
  }

  // Apply priority filter
  if (boardStore.filters.priority) {
    cards = cards.filter(card => card.priority === boardStore.filters.priority);
  }

  // Apply assignee filter
  if (boardStore.filters.assignee) {
    cards = cards.filter(card => card.assignedTo === boardStore.filters.assignee);
  }

  // Apply tag filter
  if (boardStore.filters.tags.length > 0) {
    cards = cards.filter(card =>
      card.tags.some(tag => boardStore.filters.tags.includes(tag))
    );
  }

  // Apply sorting
  cards = [...cards].sort((a, b) => {
    let comparison = 0;

    switch (boardStore.sortBy) {
      case 'date':
        comparison = a.timestamp.getTime() - b.timestamp.getTime();
        break;
      case 'priority':
        const priorityOrder = { high: 3, medium: 2, low: 1 };
        comparison = priorityOrder[a.priority as keyof typeof priorityOrder] -
                    priorityOrder[b.priority as keyof typeof priorityOrder];
        break;
      case 'assignee':
        comparison = (a.assignedTo || '').localeCompare(b.assignedTo || '');
        break;
    }

    return boardStore.sortOrder === 'desc' ? -comparison : comparison;
  });

  return cards;
});

const handleDragOver = (event: DragEvent) => {
  event.preventDefault();
  event.dataTransfer!.dropEffect = 'move';
};

const handleDragEnter = (event: DragEvent) => {
  event.preventDefault();
  dragCounter.value++;
  isDragOver.value = true;
};

const handleDragLeave = (event: DragEvent) => {
  event.preventDefault();
  dragCounter.value--;
  if (dragCounter.value === 0) {
    isDragOver.value = false;
  }
};

const handleDrop = (event: DragEvent) => {
  event.preventDefault();
  isDragOver.value = false;
  dragCounter.value = 0;

  const data = event.dataTransfer?.getData('text/plain');
  if (!data) return;

  try {
    // Try to parse as array (multiple emails)
    const emailIds = JSON.parse(data);
    if (Array.isArray(emailIds)) {
      boardStore.bulkMoveEmails(emailIds, props.column.id);
    } else {
      // Single email
      boardStore.moveEmail(data, props.column.id);
    }
  } catch {
    // Single email ID
    boardStore.moveEmail(data, props.column.id);
  }

  boardStore.setDraggedEmailId(null);
};

const handleEmailDragStart = (emailId: string) => {
  // This is handled by the EmailCard component
};

const handleEmailClick = (email: EmailCardType) => {
  emit('email-click', email);
};

const handleEmailRightClick = (email: EmailCardType, event: MouseEvent) => {
  emit('email-right-click', email, event);
};

const addNewEmail = () => {
  emit('add-email', props.column.id);
  showColumnMenu.value = false;
};

const selectAllInColumn = () => {
  boardStore.setMultiSelectMode(true);
  filteredCards.value.forEach(email => {
    if (!boardStore.selectedEmails.has(email.id)) {
      boardStore.toggleEmailSelection(email.id);
    }
  });
  showColumnMenu.value = false;
};

const clearColumnSelection = () => {
  filteredCards.value.forEach(email => {
    if (boardStore.selectedEmails.has(email.id)) {
      boardStore.toggleEmailSelection(email.id);
    }
  });
  showColumnMenu.value = false;
};

const openEmailActions = () => {
  emit('open-email-actions');
  showColumnMenu.value = false;
};

const openSequenceBuilder = () => {
  emit('open-sequence-builder');
  showColumnMenu.value = false;
};

const openBroadcastBuilder = () => {
  emit('open-broadcast-builder');
  showColumnMenu.value = false;
};

const openTemplateBuilder = () => {
  emit('open-template-builder');
  showColumnMenu.value = false;
};

const viewColumnAnalytics = () => {
  emit('view-column-analytics', props.column.id);
  showColumnMenu.value = false;
};

const exportColumnData = () => {
  emit('export-column-data', props.column.id);
  showColumnMenu.value = false;
};

const editColumn = () => {
  emit('edit-column', props.column.id);
  showColumnMenu.value = false;
};

// Close menu when clicking outside
const closeMenu = () => {
  showColumnMenu.value = false;
};

// Add click outside listener
document.addEventListener('click', closeMenu);
</script>
