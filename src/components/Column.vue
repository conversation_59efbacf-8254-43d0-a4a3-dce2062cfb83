<template>
  <div class="flex-shrink-0 w-80">
    <div :class="`rounded-lg border-2 border-dashed ${column.color} min-h-96 p-4 bg-white`">
      <!-- Column Header -->
      <div class="flex items-center justify-between mb-4">
        <div class="flex items-center space-x-2">
          <h2 class="font-semibold text-gray-800">{{ column.title }}</h2>
          <span class="bg-gray-200 text-gray-700 text-xs px-2 py-1 rounded-full">
            {{ column.cards.length }}
          </span>
          <Zap v-if="column.aiEnabled" class="w-4 h-4 text-blue-500" title="AI Aktif" />
        </div>
        <div class="flex items-center space-x-1">
          <button
            @click="$emit('add-email', column.id)"
            class="p-1 text-gray-400 hover:text-gray-600 transition-colors"
          >
            <Plus class="w-4 h-4" />
          </button>
          <button class="p-1 text-gray-400 hover:text-gray-600 transition-colors">
            <Settings class="w-4 h-4" />
          </button>
        </div>
      </div>

      <!-- Drop Zone -->
      <div
        @dragover="$emit('drag-over', $event)"
        @drop="$emit('drop', $event, column.id)"
        class="min-h-80"
      >
        <EmailCard
          v-for="email in column.cards"
          :key="email.id"
          :email="email"
          @drag-start="$emit('drag-start', email.id)"
          @click="$emit('email-click', email)"
        />
        
        <div v-if="column.cards.length === 0" class="text-center py-12 text-gray-400">
          <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
            <Plus class="w-8 h-8" />
          </div>
          <p class="text-sm">Tidak ada email</p>
          <p class="text-xs mt-1">Seret email ke sini atau klik + untuk menambah</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Plus, Settings, Zap } from 'lucide-vue-next';
import type { Column as ColumnType, EmailCard as EmailCardType } from '@/types';
import EmailCard from './EmailCard.vue';

defineProps<{
  column: ColumnType;
}>();

defineEmits<{
  'drag-over': [event: DragEvent];
  'drop': [event: DragEvent, columnId: string];
  'drag-start': [emailId: string];
  'email-click': [email: EmailCardType];
  'add-email': [columnId: string];
}>();
</script>
