<template>
  <div
    v-if="isOpen"
    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
    @click.self="closeModal"
  >
    <div class="bg-white rounded-lg shadow-xl w-full max-w-6xl mx-4 max-h-[90vh] overflow-hidden">
      <!-- Header -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200">
        <div class="flex items-center space-x-3">
          <div class="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center">
            <Settings class="w-4 h-4 text-indigo-600" />
          </div>
          <div>
            <h2 class="text-lg font-semibold text-gray-900">{{ columnTitle }} Management</h2>
            <p class="text-sm text-gray-500">Manage contacts, sequences, and broadcasts</p>
          </div>
        </div>
        <button
          @click="closeModal"
          class="p-2 hover:bg-gray-100 rounded-lg transition-colors"
        >
          <X class="w-5 h-5 text-gray-400" />
        </button>
      </div>

      <!-- Tab Navigation -->
      <div class="border-b border-gray-200">
        <nav class="flex space-x-8 px-6">
          <button
            v-for="tab in tabs"
            :key="tab.id"
            @click="activeTab = tab.id"
            :class="[
              'py-4 px-1 border-b-2 font-medium text-sm transition-colors',
              activeTab === tab.id
                ? 'border-indigo-500 text-indigo-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            ]"
          >
            <div class="flex items-center space-x-2">
              <component :is="tab.icon" class="w-4 h-4" />
              <span>{{ tab.name }}</span>
              <span class="bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded-full">{{ tab.count }}</span>
            </div>
          </button>
        </nav>
      </div>

      <!-- Content -->
      <div class="p-6 max-h-[calc(90vh-200px)] overflow-y-auto">
        <!-- Contacts Tab -->
        <div v-if="activeTab === 'contacts'">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-medium text-gray-900">Contacts in {{ columnTitle }}</h3>
            <div class="flex items-center space-x-3">
              <div class="relative">
                <Search class="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  v-model="contactSearch"
                  type="text"
                  placeholder="Search contacts..."
                  class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                />
              </div>
              <button
                @click="exportContacts"
                class="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors flex items-center space-x-2"
              >
                <Download class="w-4 h-4" />
                <span>Export</span>
              </button>
            </div>
          </div>

          <!-- Contacts List -->
          <div class="bg-white border border-gray-200 rounded-lg overflow-hidden">
            <div class="grid grid-cols-12 gap-4 p-4 bg-gray-50 border-b border-gray-200 text-sm font-medium text-gray-700">
              <div class="col-span-3">Contact</div>
              <div class="col-span-2">Company</div>
              <div class="col-span-2">Phone</div>
              <div class="col-span-2">Priority</div>
              <div class="col-span-2">Added</div>
              <div class="col-span-1">Actions</div>
            </div>
            
            <div
              v-for="contact in filteredContacts"
              :key="contact.id"
              class="grid grid-cols-12 gap-4 p-4 border-b border-gray-100 hover:bg-gray-50 transition-colors"
            >
              <div class="col-span-3">
                <div class="flex items-center space-x-3">
                  <div class="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center">
                    <User class="w-4 h-4 text-indigo-600" />
                  </div>
                  <div>
                    <p class="font-medium text-gray-900">{{ contact.name }}</p>
                    <p class="text-sm text-gray-500">{{ contact.email }}</p>
                  </div>
                </div>
              </div>
              <div class="col-span-2 text-sm text-gray-900">{{ contact.company || '-' }}</div>
              <div class="col-span-2 text-sm text-gray-900">{{ contact.phone || '-' }}</div>
              <div class="col-span-2">
                <span :class="[
                  'px-2 py-1 text-xs rounded-full',
                  contact.priority === 'High' ? 'bg-red-100 text-red-700' :
                  contact.priority === 'Medium' ? 'bg-yellow-100 text-yellow-700' :
                  'bg-green-100 text-green-700'
                ]">
                  {{ contact.priority }}
                </span>
              </div>
              <div class="col-span-2 text-sm text-gray-500">{{ formatDate(contact.date) }}</div>
              <div class="col-span-1">
                <button
                  @click="editContact(contact)"
                  class="text-indigo-600 hover:text-indigo-700"
                >
                  <Edit class="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Sequences Tab -->
        <div v-if="activeTab === 'sequences'">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-medium text-gray-900">Email Sequences for {{ columnTitle }}</h3>
            <button
              @click="createNewSequence"
              class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors flex items-center space-x-2"
            >
              <Plus class="w-4 h-4" />
              <span>New Sequence</span>
            </button>
          </div>

          <!-- Sequences List -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div
              v-for="sequence in columnSequences"
              :key="sequence.id"
              class="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow"
            >
              <div class="flex items-center justify-between mb-4">
                <div class="flex items-center space-x-3">
                  <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                    <Workflow class="w-5 h-5 text-purple-600" />
                  </div>
                  <div>
                    <h4 class="font-medium text-gray-900">{{ sequence.name }}</h4>
                    <p class="text-sm text-gray-500">{{ sequence.steps.length }} steps</p>
                  </div>
                </div>
                <div class="flex items-center space-x-2">
                  <button
                    @click="toggleSequence(sequence)"
                    :class="[
                      'w-8 h-4 rounded-full transition-colors relative',
                      sequence.active ? 'bg-green-500' : 'bg-gray-300'
                    ]"
                  >
                    <div :class="[
                      'w-3 h-3 bg-white rounded-full transition-transform absolute top-0.5',
                      sequence.active ? 'translate-x-4' : 'translate-x-0.5'
                    ]"></div>
                  </button>
                </div>
              </div>
              
              <p class="text-sm text-gray-600 mb-4">{{ sequence.description }}</p>
              
              <div class="flex items-center justify-between text-sm text-gray-500 mb-4">
                <span>{{ sequence.enrolledCount }} enrolled</span>
                <span>{{ sequence.completedCount }} completed</span>
              </div>
              
              <div class="flex items-center space-x-2">
                <button
                  @click="editSequence(sequence)"
                  class="flex-1 px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Edit
                </button>
                <button
                  @click="viewSequenceStats(sequence)"
                  class="flex-1 px-3 py-2 text-sm bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                >
                  Stats
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Broadcasts Tab -->
        <div v-if="activeTab === 'broadcasts'">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-medium text-gray-900">Broadcasts for {{ columnTitle }}</h3>
            <button
              @click="createNewBroadcast"
              class="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors flex items-center space-x-2"
            >
              <Plus class="w-4 h-4" />
              <span>New Broadcast</span>
            </button>
          </div>

          <!-- Broadcasts List -->
          <div class="space-y-4">
            <div
              v-for="broadcast in columnBroadcasts"
              :key="broadcast.id"
              class="bg-white border border-gray-200 rounded-lg p-6"
            >
              <div class="flex items-center justify-between mb-4">
                <div class="flex items-center space-x-3">
                  <div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                    <Megaphone class="w-5 h-5 text-orange-600" />
                  </div>
                  <div>
                    <h4 class="font-medium text-gray-900">{{ broadcast.name }}</h4>
                    <p class="text-sm text-gray-500">{{ broadcast.subject }}</p>
                  </div>
                </div>
                <div class="flex items-center space-x-2">
                  <span :class="[
                    'px-2 py-1 text-xs rounded-full',
                    broadcast.status === 'sent' ? 'bg-green-100 text-green-700' :
                    broadcast.status === 'scheduled' ? 'bg-blue-100 text-blue-700' :
                    broadcast.status === 'draft' ? 'bg-gray-100 text-gray-700' :
                    'bg-yellow-100 text-yellow-700'
                  ]">
                    {{ broadcast.status }}
                  </span>
                </div>
              </div>
              
              <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                <div class="text-center">
                  <p class="text-2xl font-bold text-gray-900">{{ broadcast.recipients }}</p>
                  <p class="text-sm text-gray-500">Recipients</p>
                </div>
                <div class="text-center">
                  <p class="text-2xl font-bold text-green-600">{{ broadcast.opened }}</p>
                  <p class="text-sm text-gray-500">Opened</p>
                </div>
                <div class="text-center">
                  <p class="text-2xl font-bold text-blue-600">{{ broadcast.clicked }}</p>
                  <p class="text-sm text-gray-500">Clicked</p>
                </div>
                <div class="text-center">
                  <p class="text-2xl font-bold text-gray-600">{{ broadcast.openRate }}%</p>
                  <p class="text-sm text-gray-500">Open Rate</p>
                </div>
              </div>
              
              <div class="flex items-center justify-between">
                <p class="text-sm text-gray-500">
                  {{ broadcast.status === 'scheduled' ? 'Scheduled for' : 'Sent on' }} {{ formatDate(broadcast.date) }}
                </p>
                <div class="flex items-center space-x-2">
                  <button
                    v-if="broadcast.status === 'draft'"
                    @click="editBroadcast(broadcast)"
                    class="px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    Edit
                  </button>
                  <button
                    @click="viewBroadcastStats(broadcast)"
                    class="px-3 py-2 text-sm bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
                  >
                    View Details
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Analytics Tab -->
        <div v-if="activeTab === 'analytics'">
          <h3 class="text-lg font-medium text-gray-900 mb-6">{{ columnTitle }} Analytics</h3>
          
          <!-- Stats Grid -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white border border-gray-200 rounded-lg p-6">
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-sm font-medium text-gray-600">Total Contacts</p>
                  <p class="text-2xl font-bold text-gray-900">{{ analytics.totalContacts }}</p>
                </div>
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Users class="w-6 h-6 text-blue-600" />
                </div>
              </div>
              <p class="text-sm text-green-600 mt-2">+12% from last month</p>
            </div>
            
            <div class="bg-white border border-gray-200 rounded-lg p-6">
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-sm font-medium text-gray-600">Active Sequences</p>
                  <p class="text-2xl font-bold text-gray-900">{{ analytics.activeSequences }}</p>
                </div>
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                  <Workflow class="w-6 h-6 text-purple-600" />
                </div>
              </div>
              <p class="text-sm text-green-600 mt-2">+3 new this week</p>
            </div>
            
            <div class="bg-white border border-gray-200 rounded-lg p-6">
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-sm font-medium text-gray-600">Broadcasts Sent</p>
                  <p class="text-2xl font-bold text-gray-900">{{ analytics.broadcastsSent }}</p>
                </div>
                <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                  <Megaphone class="w-6 h-6 text-orange-600" />
                </div>
              </div>
              <p class="text-sm text-blue-600 mt-2">{{ analytics.avgOpenRate }}% avg open rate</p>
            </div>
            
            <div class="bg-white border border-gray-200 rounded-lg p-6">
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-sm font-medium text-gray-600">Response Rate</p>
                  <p class="text-2xl font-bold text-gray-900">{{ analytics.responseRate }}%</p>
                </div>
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <TrendingUp class="w-6 h-6 text-green-600" />
                </div>
              </div>
              <p class="text-sm text-green-600 mt-2">+5% improvement</p>
            </div>
          </div>
          
          <!-- Charts Placeholder -->
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div class="bg-white border border-gray-200 rounded-lg p-6">
              <h4 class="font-medium text-gray-900 mb-4">Contact Growth</h4>
              <div class="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
                <p class="text-gray-500">Chart placeholder - Contact growth over time</p>
              </div>
            </div>
            
            <div class="bg-white border border-gray-200 rounded-lg p-6">
              <h4 class="font-medium text-gray-900 mb-4">Email Performance</h4>
              <div class="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
                <p class="text-gray-500">Chart placeholder - Email open/click rates</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { 
  Settings, X, Search, Download, User, Edit, Plus, Workflow, 
  Megaphone, Users, TrendingUp 
} from 'lucide-vue-next';

interface Props {
  isOpen: boolean;
  columnId: string;
  columnTitle: string;
}

interface Emits {
  (e: 'close'): void;
  (e: 'create-sequence'): void;
  (e: 'create-broadcast'): void;
  (e: 'edit-contact', contact: any): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// State
const activeTab = ref('contacts');
const contactSearch = ref('');

// Tabs
const tabs = ref([
  { id: 'contacts', name: 'Contacts', icon: Users, count: 45 },
  { id: 'sequences', name: 'Sequences', icon: Workflow, count: 8 },
  { id: 'broadcasts', name: 'Broadcasts', icon: Megaphone, count: 12 },
  { id: 'analytics', name: 'Analytics', icon: TrendingUp, count: '' }
]);

// Mock data - in real app, this would come from props or API
const contacts = ref([
  {
    id: '1',
    name: 'John Doe',
    email: '<EMAIL>',
    company: 'Acme Corp',
    phone: '+1234567890',
    priority: 'High',
    date: '2024-01-15T10:00:00Z'
  },
  {
    id: '2',
    name: 'Jane Smith',
    email: '<EMAIL>',
    company: 'Startup Inc',
    phone: '+1987654321',
    priority: 'Medium',
    date: '2024-01-14T15:30:00Z'
  }
]);

const columnSequences = ref([
  {
    id: '1',
    name: 'Welcome Series',
    description: 'Onboarding sequence for new contacts',
    steps: [1, 2, 3],
    active: true,
    enrolledCount: 23,
    completedCount: 18
  },
  {
    id: '2',
    name: 'Follow-up Campaign',
    description: 'Follow-up for interested prospects',
    steps: [1, 2],
    active: false,
    enrolledCount: 15,
    completedCount: 8
  }
]);

const columnBroadcasts = ref([
  {
    id: '1',
    name: 'Monthly Newsletter',
    subject: 'Your monthly update is here!',
    status: 'sent',
    recipients: 156,
    opened: 89,
    clicked: 23,
    openRate: 57,
    date: '2024-01-10T09:00:00Z'
  },
  {
    id: '2',
    name: 'Product Launch',
    subject: 'Introducing our new feature',
    status: 'scheduled',
    recipients: 203,
    opened: 0,
    clicked: 0,
    openRate: 0,
    date: '2024-01-20T10:00:00Z'
  }
]);

const analytics = ref({
  totalContacts: 156,
  activeSequences: 5,
  broadcastsSent: 12,
  responseRate: 23,
  avgOpenRate: 45
});

// Computed
const filteredContacts = computed(() => {
  if (!contactSearch.value) return contacts.value;
  
  const search = contactSearch.value.toLowerCase();
  return contacts.value.filter(contact =>
    contact.name.toLowerCase().includes(search) ||
    contact.email.toLowerCase().includes(search) ||
    contact.company?.toLowerCase().includes(search)
  );
});

// Methods
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString();
};

const exportContacts = () => {
  console.log('Exporting contacts for column:', props.columnId);
  // Implement CSV export
};

const editContact = (contact: any) => {
  emit('edit-contact', contact);
};

const createNewSequence = () => {
  emit('create-sequence');
};

const createNewBroadcast = () => {
  emit('create-broadcast');
};

const editSequence = (sequence: any) => {
  console.log('Edit sequence:', sequence);
};

const viewSequenceStats = (sequence: any) => {
  console.log('View sequence stats:', sequence);
};

const toggleSequence = (sequence: any) => {
  sequence.active = !sequence.active;
  console.log('Toggle sequence:', sequence.id, sequence.active);
};

const editBroadcast = (broadcast: any) => {
  console.log('Edit broadcast:', broadcast);
};

const viewBroadcastStats = (broadcast: any) => {
  console.log('View broadcast stats:', broadcast);
};

const closeModal = () => {
  emit('close');
};
</script>
