<template>
  <div v-if="isOpen" class="fixed inset-0 z-50 overflow-y-auto">
    <!-- Backdrop -->
    <div 
      class="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
      @click="closeModal"
    ></div>
    
    <!-- Modal -->
    <div class="flex min-h-full items-center justify-center p-4">
      <div class="relative bg-white rounded-lg shadow-xl max-w-5xl w-full max-h-[90vh] overflow-hidden animate-fade-in">
        <!-- Header -->
        <div class="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 class="text-xl font-semibold text-gray-900">Email Actions for {{ email?.subject }}</h2>
          <button
            @click="closeModal"
            class="p-2 text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X class="w-5 h-5" />
          </button>
        </div>

        <!-- Action Selection -->
        <div class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
            <!-- Email Sequences -->
            <div
              @click="selectAction('sequence')"
              class="p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 hover:shadow-md"
              :class="selectedAction === 'sequence' ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-blue-300'"
            >
              <div class="flex items-center space-x-3 mb-3">
                <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                  <Workflow class="w-5 h-5 text-purple-600" />
                </div>
                <div>
                  <h3 class="font-medium text-gray-900">Email Sequence</h3>
                  <p class="text-xs text-gray-500">Automated follow-ups</p>
                </div>
              </div>
              <p class="text-sm text-gray-600">Create automated email sequences based on triggers</p>
            </div>

            <!-- Email Broadcast -->
            <div
              @click="selectAction('broadcast')"
              class="p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 hover:shadow-md"
              :class="selectedAction === 'broadcast' ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-blue-300'"
            >
              <div class="flex items-center space-x-3 mb-3">
                <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                  <Megaphone class="w-5 h-5 text-green-600" />
                </div>
                <div>
                  <h3 class="font-medium text-gray-900">Email Broadcast</h3>
                  <p class="text-xs text-gray-500">Mass email sending</p>
                </div>
              </div>
              <p class="text-sm text-gray-600">Send emails to multiple contacts at once</p>
            </div>

            <!-- Email Templates -->
            <div
              @click="selectAction('template')"
              class="p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 hover:shadow-md"
              :class="selectedAction === 'template' ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-blue-300'"
            >
              <div class="flex items-center space-x-3 mb-3">
                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <FileText class="w-5 h-5 text-blue-600" />
                </div>
                <div>
                  <h3 class="font-medium text-gray-900">Email Template</h3>
                  <p class="text-xs text-gray-500">Reusable layouts</p>
                </div>
              </div>
              <p class="text-sm text-gray-600">Create and manage reusable email templates</p>
            </div>

            <!-- Email Tracking -->
            <div
              @click="selectAction('tracking')"
              class="p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 hover:shadow-md"
              :class="selectedAction === 'tracking' ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-blue-300'"
            >
              <div class="flex items-center space-x-3 mb-3">
                <div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                  <BarChart3 class="w-5 h-5 text-orange-600" />
                </div>
                <div>
                  <h3 class="font-medium text-gray-900">Email Tracking</h3>
                  <p class="text-xs text-gray-500">Analytics & metrics</p>
                </div>
              </div>
              <p class="text-sm text-gray-600">Track opens, clicks, and engagement</p>
            </div>

            <!-- Personalization -->
            <div
              @click="selectAction('personalization')"
              class="p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 hover:shadow-md"
              :class="selectedAction === 'personalization' ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-blue-300'"
            >
              <div class="flex items-center space-x-3 mb-3">
                <div class="w-10 h-10 bg-teal-100 rounded-lg flex items-center justify-center">
                  <User class="w-5 h-5 text-teal-600" />
                </div>
                <div>
                  <h3 class="font-medium text-gray-900">Personalization</h3>
                  <p class="text-xs text-gray-500">Dynamic content</p>
                </div>
              </div>
              <p class="text-sm text-gray-600">Add personalization tags and dynamic content</p>
            </div>

            <!-- Export -->
            <div
              @click="selectAction('export')"
              class="p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 hover:shadow-md"
              :class="selectedAction === 'export' ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-blue-300'"
            >
              <div class="flex items-center space-x-3 mb-3">
                <div class="w-10 h-10 bg-indigo-100 rounded-lg flex items-center justify-center">
                  <Download class="w-5 h-5 text-indigo-600" />
                </div>
                <div>
                  <h3 class="font-medium text-gray-900">Export</h3>
                  <p class="text-xs text-gray-500">Data export</p>
                </div>
              </div>
              <p class="text-sm text-gray-600">Export emails and analytics data</p>
            </div>
          </div>

          <!-- Action-specific Content -->
          <div v-if="selectedAction" class="border-t border-gray-200 pt-6">
            <!-- Email Sequence -->
            <div v-if="selectedAction === 'sequence'" class="space-y-4">
              <h3 class="text-lg font-medium text-gray-900">Create Email Sequence</h3>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Sequence Name</label>
                  <input
                    v-model="sequenceForm.name"
                    type="text"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                    placeholder="Welcome Series"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Trigger</label>
                  <select
                    v-model="sequenceForm.trigger"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="new_contact">New Contact</option>
                    <option value="email_opened">Email Opened</option>
                    <option value="link_clicked">Link Clicked</option>
                    <option value="no_reply">No Reply After X Days</option>
                    <option value="tag_added">Tag Added</option>
                  </select>
                </div>
              </div>
              <div class="bg-gray-50 rounded-lg p-4">
                <h4 class="font-medium mb-3">Sequence Steps</h4>
                <div class="space-y-3">
                  <div v-for="(step, index) in sequenceForm.steps" :key="index" class="flex items-center space-x-3 bg-white p-3 rounded border">
                    <span class="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium">{{ index + 1 }}</span>
                    <div class="flex-1">
                      <input
                        v-model="step.subject"
                        type="text"
                        placeholder="Email subject"
                        class="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                      />
                    </div>
                    <div class="w-24">
                      <input
                        v-model="step.delay"
                        type="number"
                        placeholder="Days"
                        class="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                      />
                    </div>
                    <button @click="removeSequenceStep(index)" class="text-red-500 hover:text-red-700">
                      <X class="w-4 h-4" />
                    </button>
                  </div>
                  <button @click="addSequenceStep" class="w-full p-2 border-2 border-dashed border-gray-300 rounded-lg text-gray-500 hover:border-blue-400 hover:text-blue-600">
                    + Add Step
                  </button>
                </div>
              </div>
            </div>

            <!-- Email Broadcast -->
            <div v-if="selectedAction === 'broadcast'" class="space-y-4">
              <h3 class="text-lg font-medium text-gray-900">Create Email Broadcast</h3>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Campaign Name</label>
                  <input
                    v-model="broadcastForm.name"
                    type="text"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                    placeholder="Monthly Newsletter"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Send To</label>
                  <select
                    v-model="broadcastForm.audience"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="all">All Contacts</option>
                    <option value="customers">Customers Only</option>
                    <option value="prospects">Prospects Only</option>
                    <option value="tagged">Specific Tags</option>
                  </select>
                </div>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Subject Line</label>
                <input
                  v-model="broadcastForm.subject"
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  placeholder="Your monthly update is here!"
                />
              </div>
              <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div class="flex items-center space-x-2 mb-2">
                  <Clock class="w-4 h-4 text-blue-600" />
                  <span class="text-sm font-medium text-blue-900">Schedule Options</span>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <label class="flex items-center space-x-2">
                    <input type="radio" v-model="broadcastForm.schedule" value="now" class="text-blue-600" />
                    <span class="text-sm">Send Now</span>
                  </label>
                  <label class="flex items-center space-x-2">
                    <input type="radio" v-model="broadcastForm.schedule" value="later" class="text-blue-600" />
                    <span class="text-sm">Schedule for Later</span>
                  </label>
                </div>
                <div v-if="broadcastForm.schedule === 'later'" class="mt-3 grid grid-cols-1 md:grid-cols-2 gap-4">
                  <input
                    v-model="broadcastForm.scheduleDate"
                    type="date"
                    class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  />
                  <input
                    v-model="broadcastForm.scheduleTime"
                    type="time"
                    class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>
            </div>

            <!-- Email Template -->
            <div v-if="selectedAction === 'template'" class="space-y-4">
              <h3 class="text-lg font-medium text-gray-900">Create Email Template</h3>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Template Name</label>
                  <input
                    v-model="templateForm.name"
                    type="text"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                    placeholder="Support Response Template"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Category</label>
                  <select
                    v-model="templateForm.category"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="support">Support</option>
                    <option value="sales">Sales</option>
                    <option value="marketing">Marketing</option>
                    <option value="onboarding">Onboarding</option>
                    <option value="follow-up">Follow-up</option>
                  </select>
                </div>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Subject Template</label>
                <input
                  v-model="templateForm.subject"
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  placeholder="Re: {{original_subject}} - We're here to help!"
                />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Email Content</label>
                <textarea
                  v-model="templateForm.content"
                  rows="8"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  placeholder="Hi {{first_name}},&#10;&#10;Thank you for contacting us..."
                ></textarea>
              </div>
              <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <h4 class="font-medium text-yellow-900 mb-2">Available Personalization Tags:</h4>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm">
                  <code class="bg-white px-2 py-1 rounded">{{first_name}}</code>
                  <code class="bg-white px-2 py-1 rounded">{{last_name}}</code>
                  <code class="bg-white px-2 py-1 rounded">{{email}}</code>
                  <code class="bg-white px-2 py-1 rounded">{{company}}</code>
                  <code class="bg-white px-2 py-1 rounded">{{original_subject}}</code>
                  <code class="bg-white px-2 py-1 rounded">{{agent_name}}</code>
                  <code class="bg-white px-2 py-1 rounded">{{current_date}}</code>
                  <code class="bg-white px-2 py-1 rounded">{{ticket_id}}</code>
                </div>
              </div>
            </div>

            <!-- Email Tracking -->
            <div v-if="selectedAction === 'tracking'" class="space-y-4">
              <h3 class="text-lg font-medium text-gray-900">Email Tracking & Analytics</h3>
              <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div class="flex items-center space-x-2 mb-2">
                    <Eye class="w-5 h-5 text-green-600" />
                    <span class="font-medium text-green-900">Open Rate</span>
                  </div>
                  <div class="text-2xl font-bold text-green-900">68%</div>
                  <div class="text-sm text-green-700">Last 30 days</div>
                </div>
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div class="flex items-center space-x-2 mb-2">
                    <MousePointer class="w-5 h-5 text-blue-600" />
                    <span class="font-medium text-blue-900">Click Rate</span>
                  </div>
                  <div class="text-2xl font-bold text-blue-900">24%</div>
                  <div class="text-sm text-blue-700">Last 30 days</div>
                </div>
                <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
                  <div class="flex items-center space-x-2 mb-2">
                    <Reply class="w-5 h-5 text-purple-600" />
                    <span class="font-medium text-purple-900">Reply Rate</span>
                  </div>
                  <div class="text-2xl font-bold text-purple-900">12%</div>
                  <div class="text-sm text-purple-700">Last 30 days</div>
                </div>
              </div>
            </div>

            <!-- Export -->
            <div v-if="selectedAction === 'export'" class="space-y-4">
              <h3 class="text-lg font-medium text-gray-900">Export Data</h3>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <button class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 text-left">
                  <div class="font-medium">Export Emails</div>
                  <div class="text-sm text-gray-500">Download all email data as CSV</div>
                </button>
                <button class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 text-left">
                  <div class="font-medium">Export Analytics</div>
                  <div class="text-sm text-gray-500">Download performance metrics</div>
                </button>
                <button class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 text-left">
                  <div class="font-medium">Export Contacts</div>
                  <div class="text-sm text-gray-500">Download contact database</div>
                </button>
                <button class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 text-left">
                  <div class="font-medium">Export Templates</div>
                  <div class="text-sm text-gray-500">Download email templates</div>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Footer -->
        <div class="flex items-center justify-end p-6 border-t border-gray-200 space-x-3">
          <button
            @click="closeModal"
            class="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          <button
            v-if="selectedAction && selectedAction !== 'tracking' && selectedAction !== 'export'"
            @click="executeAction"
            class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            {{ getActionButtonText() }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import {
  X, Workflow, Megaphone, FileText, BarChart3, User, Download,
  Clock, Eye, MousePointer, Reply
} from 'lucide-vue-next';
import type { EmailCard } from '@/types';

const props = defineProps<{
  isOpen: boolean;
  email: EmailCard | null;
}>();

const emit = defineEmits<{
  'close': [];
  'action-created': [action: any];
}>();

const selectedAction = ref<string>('');

const sequenceForm = reactive({
  name: '',
  trigger: 'new_contact',
  steps: [
    { subject: '', delay: 1 }
  ]
});

const broadcastForm = reactive({
  name: '',
  audience: 'all',
  subject: '',
  schedule: 'now',
  scheduleDate: '',
  scheduleTime: ''
});

const templateForm = reactive({
  name: '',
  category: 'support',
  subject: '',
  content: ''
});

const selectAction = (action: string) => {
  selectedAction.value = action;
};

const addSequenceStep = () => {
  sequenceForm.steps.push({ subject: '', delay: 1 });
};

const removeSequenceStep = (index: number) => {
  sequenceForm.steps.splice(index, 1);
};

const getActionButtonText = () => {
  switch (selectedAction.value) {
    case 'sequence': return 'Create Sequence';
    case 'broadcast': return 'Create Broadcast';
    case 'template': return 'Save Template';
    case 'personalization': return 'Apply Personalization';
    default: return 'Continue';
  }
};

const executeAction = () => {
  let actionData;
  
  switch (selectedAction.value) {
    case 'sequence':
      actionData = { type: 'sequence', data: sequenceForm };
      break;
    case 'broadcast':
      actionData = { type: 'broadcast', data: broadcastForm };
      break;
    case 'template':
      actionData = { type: 'template', data: templateForm };
      break;
    default:
      actionData = { type: selectedAction.value, data: {} };
  }
  
  emit('action-created', actionData);
  closeModal();
};

const closeModal = () => {
  selectedAction.value = '';
  emit('close');
};
</script>
