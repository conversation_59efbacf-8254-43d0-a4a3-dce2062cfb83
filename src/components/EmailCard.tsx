import React, { useState } from 'react';
import { Clock, Paperclip, User, Tag, Brain, MessageSquare, Calendar } from 'lucide-react';
import { EmailCard as EmailCardType, Team } from '../types';
import { getPriorityColor, getSentimentIcon, getSentimentColor, formatTimeAgo, generateAIInsight } from '../utils/ai';

interface EmailCardProps {
  email: EmailCardType;
  team: Team[];
  onDragStart: (e: React.DragEvent, emailId: string) => void;
  onClick: (email: EmailCardType) => void;
}

const EmailCard: React.FC<EmailCardProps> = ({ email, team, onDragStart, onClick }) => {
  const [showAIInsight, setShowAIInsight] = useState(false);
  const assignedUser = team.find(member => member.id === email.assignedTo);

  return (
    <div
      draggable
      onDragStart={(e) => onDragStart(e, email.id)}
      onClick={() => onClick(email)}
      className="bg-white rounded-lg border border-gray-200 p-4 mb-3 cursor-pointer hover:shadow-lg transition-all duration-200 group hover:border-blue-300"
    >
      {/* Header */}
      <div className="flex items-start justify-between mb-2">
        <div className="flex items-center space-x-2">
          <span className={`text-xs px-2 py-1 rounded-full border ${getPriorityColor(email.priority)}`}>
            {email.priority === 'high' ? 'Tinggi' : email.priority === 'medium' ? 'Sedang' : 'Rendah'}
          </span>
          {!email.isRead && (
            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
          )}
        </div>
        <div className="flex items-center space-x-1">
          <span className={`text-sm ${getSentimentColor(email.sentiment)}`}>
            {getSentimentIcon(email.sentiment)}
          </span>
          <button
            onClick={(e) => {
              e.stopPropagation();
              setShowAIInsight(!showAIInsight);
            }}
            className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
          >
            <Brain className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* AI Insight */}
      {showAIInsight && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-3">
          <div className="flex items-start space-x-2">
            <Brain className="w-4 h-4 text-blue-600 mt-0.5" />
            <div>
              <p className="text-xs font-medium text-blue-900 mb-1">AI Insight</p>
              <p className="text-xs text-blue-700">{generateAIInsight(email)}</p>
            </div>
          </div>
        </div>
      )}

      {/* Subject */}
      <h3 className="font-semibold text-gray-900 mb-2 group-hover:text-blue-700 transition-colors">
        {email.subject}
      </h3>

      {/* From */}
      <p className="text-sm text-gray-600 mb-2">Dari: {email.from}</p>

      {/* Content Preview */}
      <p className="text-sm text-gray-700 mb-3 line-clamp-2">
        {email.content}
      </p>

      {/* AI Summary */}
      {email.aiSummary && (
        <div className="bg-gray-50 rounded-lg p-2 mb-3">
          <p className="text-xs text-gray-600">
            <Brain className="w-3 h-3 inline mr-1" />
            {email.aiSummary}
          </p>
        </div>
      )}

      {/* Tags */}
      {email.tags.length > 0 && (
        <div className="flex flex-wrap gap-1 mb-3">
          {email.tags.map((tag, index) => (
            <span
              key={index}
              className="inline-flex items-center text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded-full"
            >
              <Tag className="w-3 h-3 mr-1" />
              {tag}
            </span>
          ))}
        </div>
      )}

      {/* Footer */}
      <div className="flex items-center justify-between text-xs text-gray-500">
        <div className="flex items-center space-x-3">
          <div className="flex items-center space-x-1">
            <Clock className="w-3 h-3" />
            <span>{formatTimeAgo(email.timestamp)}</span>
          </div>
          
          {email.attachments > 0 && (
            <div className="flex items-center space-x-1">
              <Paperclip className="w-3 h-3" />
              <span>{email.attachments}</span>
            </div>
          )}

          {email.followUpDate && (
            <div className="flex items-center space-x-1 text-orange-600">
              <Calendar className="w-3 h-3" />
              <span>Follow up</span>
            </div>
          )}
        </div>

        {assignedUser && (
          <div className="flex items-center space-x-1">
            <User className="w-3 h-3" />
            <img
              src={assignedUser.avatar}
              alt={assignedUser.name}
              className="w-4 h-4 rounded-full"
            />
            <span className="text-xs">{assignedUser.name.split(' ')[0]}</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default EmailCard;