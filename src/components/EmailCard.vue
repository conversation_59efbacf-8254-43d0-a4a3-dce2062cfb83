<template>
  <div
    draggable="true"
    @dragstart="$emit('drag-start')"
    @click="$emit('click')"
    class="bg-white rounded-lg border border-gray-200 p-4 mb-3 cursor-pointer hover:shadow-lg transition-all duration-200 group hover:border-blue-300 hover:scale-[1.02] transform"
  >
    <!-- Header -->
    <div class="flex items-start justify-between mb-2">
      <div class="flex items-center space-x-2">
        <span :class="`text-xs px-2 py-1 rounded-full border ${getPriorityColor(email.priority)}`">
          {{ getPriorityText(email.priority) }}
        </span>
        <div v-if="!email.isRead" class="w-2 h-2 bg-blue-500 rounded-full"></div>
      </div>
      <div class="flex items-center space-x-1">
        <span :class="`text-sm ${getSentimentColor(email.sentiment)}`">
          {{ getSentimentIcon(email.sentiment) }}
        </span>
        <button
          @click.stop="showAIInsight = !showAIInsight"
          class="p-1 text-gray-400 hover:text-blue-600 transition-colors"
        >
          <Brain class="w-4 h-4" />
        </button>
      </div>
    </div>

    <!-- AI Insight -->
    <div v-if="showAIInsight" class="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-3 mb-3 animate-fade-in">
      <div class="flex items-start space-x-2">
        <Brain class="w-4 h-4 text-blue-600 mt-0.5 animate-pulse" />
        <div>
          <p class="text-xs font-medium text-blue-900 mb-1 flex items-center">
            AI Insight
            <span class="ml-1 w-1 h-1 bg-blue-500 rounded-full"></span>
          </p>
          <p class="text-xs text-blue-700">{{ generateAIInsight(email) }}</p>
        </div>
      </div>
    </div>

    <!-- Subject -->
    <h3 class="font-semibold text-gray-900 mb-2 group-hover:text-blue-700 transition-colors">
      {{ email.subject }}
    </h3>

    <!-- From -->
    <p class="text-sm text-gray-600 mb-2">From: {{ email.from }}</p>

    <!-- Content Preview -->
    <p class="text-sm text-gray-700 mb-3 overflow-hidden" style="display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical;">
      {{ email.content }}
    </p>

    <!-- AI Summary -->
    <div v-if="email.aiSummary" class="bg-gray-50 rounded-lg p-2 mb-3">
      <p class="text-xs text-gray-600">
        <Brain class="w-3 h-3 inline mr-1" />
        {{ email.aiSummary }}
      </p>
    </div>

    <!-- Tags -->
    <div v-if="email.tags.length > 0" class="flex flex-wrap gap-1 mb-3">
      <span
        v-for="(tag, index) in email.tags"
        :key="index"
        class="inline-flex items-center text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded-full"
      >
        <Tag class="w-3 h-3 mr-1" />
        {{ tag }}
      </span>
    </div>

    <!-- Footer -->
    <div class="flex items-center justify-between text-xs text-gray-500">
      <div class="flex items-center space-x-3">
        <div class="flex items-center space-x-1">
          <Clock class="w-3 h-3" />
          <span>{{ formatTimeAgo(email.timestamp) }}</span>
        </div>
        
        <div v-if="email.attachments > 0" class="flex items-center space-x-1">
          <Paperclip class="w-3 h-3" />
          <span>{{ email.attachments }}</span>
        </div>

        <div v-if="email.followUpDate" class="flex items-center space-x-1 text-orange-600">
          <Calendar class="w-3 h-3" />
          <span>Follow up</span>
        </div>
      </div>

      <div v-if="assignedUser" class="flex items-center space-x-1">
        <User class="w-3 h-3" />
        <img
          :src="assignedUser.avatar"
          :alt="assignedUser.name"
          class="w-4 h-4 rounded-full"
        />
        <span class="text-xs">{{ assignedUser.name.split(' ')[0] }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { Clock, Paperclip, User, Tag, Brain, Calendar } from 'lucide-vue-next';
import type { EmailCard as EmailCardType } from '@/types';
import { getPriorityColor, getSentimentIcon, getSentimentColor, formatTimeAgo, generateAIInsight } from '@/utils/ai';
import { mockTeam } from '@/data/mockData';

const props = defineProps<{
  email: EmailCardType;
}>();

defineEmits<{
  'drag-start': [];
  'click': [];
}>();

const showAIInsight = ref(false);

const assignedUser = computed(() => 
  mockTeam.find(member => member.id === props.email.assignedTo)
);

const getPriorityText = (priority: string) => {
  switch (priority) {
    case 'high': return 'High';
    case 'medium': return 'Medium';
    case 'low': return 'Low';
    default: return priority;
  }
};
</script>
