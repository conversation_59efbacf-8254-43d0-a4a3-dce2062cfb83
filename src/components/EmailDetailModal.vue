<template>
  <div v-if="isOpen" class="fixed inset-0 z-50 overflow-y-auto">
    <!-- Backdrop -->
    <div 
      class="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
      @click="closeModal"
    ></div>
    
    <!-- Modal -->
    <div class="flex min-h-full items-center justify-center p-4">
      <div class="relative bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden animate-fade-in">
        <!-- Header -->
        <div class="flex items-center justify-between p-6 border-b border-gray-200">
          <div class="flex items-center space-x-3">
            <div :class="`w-3 h-3 rounded-full ${getPriorityColor(email?.priority || 'low')}`"></div>
            <h2 class="text-xl font-semibold text-gray-900">{{ email?.subject }}</h2>
            <span v-if="!email?.isRead" class="w-2 h-2 bg-blue-500 rounded-full"></span>
          </div>
          <div class="flex items-center space-x-2">
            <button
              @click="toggleAIInsight"
              class="p-2 text-gray-400 hover:text-blue-600 transition-colors"
              :class="{ 'text-blue-600': showAIInsight }"
            >
              <Brain class="w-5 h-5" />
            </button>
            <button
              @click="closeModal"
              class="p-2 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X class="w-5 h-5" />
            </button>
          </div>
        </div>

        <!-- AI Insight -->
        <div v-if="showAIInsight && email" class="bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-blue-200 p-4">
          <div class="flex items-start space-x-3">
            <Brain class="w-5 h-5 text-blue-600 mt-0.5 animate-pulse" />
            <div>
              <h3 class="font-medium text-blue-900 mb-2">AI Analysis</h3>
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <span class="text-blue-700 font-medium">Sentiment:</span>
                  <span class="ml-1 text-blue-600">{{ getSentimentText(email.sentiment) }}</span>
                </div>
                <div>
                  <span class="text-blue-700 font-medium">Category:</span>
                  <span class="ml-1 text-blue-600">{{ email.category }}</span>
                </div>
                <div>
                  <span class="text-blue-700 font-medium">Priority:</span>
                  <span class="ml-1 text-blue-600">{{ getPriorityText(email.priority) }}</span>
                </div>
              </div>
              <p class="text-blue-700 mt-2">{{ generateAIInsight(email) }}</p>
            </div>
          </div>
        </div>

        <!-- Content -->
        <div class="flex-1 overflow-y-auto max-h-96">
          <!-- Email Meta -->
          <div class="p-6 border-b border-gray-200">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <span class="text-gray-600 font-medium">From:</span>
                <span class="ml-2 text-gray-900">{{ email?.from }}</span>
              </div>
              <div>
                <span class="text-gray-600 font-medium">To:</span>
                <span class="ml-2 text-gray-900">{{ email?.to }}</span>
              </div>
              <div>
                <span class="text-gray-600 font-medium">Date:</span>
                <span class="ml-2 text-gray-900">{{ formatDate(email?.timestamp) }}</span>
              </div>
              <div v-if="email?.attachments && email.attachments > 0">
                <span class="text-gray-600 font-medium">Attachments:</span>
                <span class="ml-2 text-gray-900 flex items-center">
                  <Paperclip class="w-4 h-4 mr-1" />
                  {{ email.attachments }} file(s)
                </span>
              </div>
            </div>
            
            <!-- Tags -->
            <div v-if="email?.tags && email.tags.length > 0" class="mt-4">
              <span class="text-gray-600 font-medium text-sm">Tags:</span>
              <div class="flex flex-wrap gap-2 mt-2">
                <span
                  v-for="tag in email.tags"
                  :key="tag"
                  class="inline-flex items-center text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded-full"
                >
                  <Tag class="w-3 h-3 mr-1" />
                  {{ tag }}
                </span>
              </div>
            </div>
          </div>

          <!-- Email Content -->
          <div class="p-6">
            <div class="prose max-w-none">
              <p class="text-gray-900 whitespace-pre-wrap">{{ email?.content }}</p>
            </div>
          </div>
        </div>

        <!-- Actions -->
        <div class="flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50">
          <div class="flex items-center space-x-3">
            <!-- Priority -->
            <select
              v-model="localPriority"
              @change="updatePriority"
              class="text-sm border border-gray-300 rounded-md px-3 py-1 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="low">Low Priority</option>
              <option value="medium">Medium Priority</option>
              <option value="high">High Priority</option>
            </select>

            <!-- Assignee -->
            <select
              v-model="localAssignee"
              @change="updateAssignee"
              class="text-sm border border-gray-300 rounded-md px-3 py-1 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Unassigned</option>
              <option v-for="member in team" :key="member.id" :value="member.id">
                {{ member.name }}
              </option>
            </select>
          </div>

          <div class="flex items-center space-x-2">
            <button
              @click="replyToEmail"
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
            >
              <Reply class="w-4 h-4 mr-2 inline" />
              Reply
            </button>
            <button
              @click="forwardEmail"
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
            >
              <Forward class="w-4 h-4 mr-2 inline" />
              Forward
            </button>
            <button
              @click="deleteEmail"
              class="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700 transition-colors"
            >
              <Trash2 class="w-4 h-4 mr-2 inline" />
              Delete
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { X, Brain, Paperclip, Tag, Reply, Forward, Trash2 } from 'lucide-vue-next';
import type { EmailCard } from '@/types';
import { getPriorityColor, getSentimentText, generateAIInsight, formatTimeAgo } from '@/utils/ai';
import { mockTeam } from '@/data/mockData';
import { useBoardStore } from '@/stores/board';

const props = defineProps<{
  email: EmailCard | null;
  isOpen: boolean;
}>();

const emit = defineEmits<{
  'close': [];
  'reply': [email: EmailCard];
  'forward': [email: EmailCard];
  'delete': [emailId: string];
}>();

const boardStore = useBoardStore();
const showAIInsight = ref(false);
const localPriority = ref(props.email?.priority || 'low');
const localAssignee = ref(props.email?.assignedTo || '');

const team = mockTeam;

// Watch for email changes
watch(() => props.email, (newEmail) => {
  if (newEmail) {
    localPriority.value = newEmail.priority;
    localAssignee.value = newEmail.assignedTo || '';
  }
});

const formatDate = (date?: Date) => {
  if (!date) return '';
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date);
};

const getPriorityText = (priority: string) => {
  switch (priority) {
    case 'high': return 'High';
    case 'medium': return 'Medium';
    case 'low': return 'Low';
    default: return priority;
  }
};

const toggleAIInsight = () => {
  showAIInsight.value = !showAIInsight.value;
};

const closeModal = () => {
  emit('close');
};

const updatePriority = () => {
  if (props.email) {
    boardStore.updateEmail(props.email.id, { priority: localPriority.value });
  }
};

const updateAssignee = () => {
  if (props.email) {
    boardStore.updateEmail(props.email.id, { assignedTo: localAssignee.value || undefined });
  }
};

const replyToEmail = () => {
  if (props.email) {
    emit('reply', props.email);
  }
};

const forwardEmail = () => {
  if (props.email) {
    emit('forward', props.email);
  }
};

const deleteEmail = () => {
  if (props.email) {
    emit('delete', props.email.id);
    closeModal();
  }
};
</script>
