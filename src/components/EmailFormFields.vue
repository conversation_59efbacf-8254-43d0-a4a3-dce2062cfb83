<template>
  <div class="space-y-6">
    <!-- From Field -->
    <div v-if="showField('from')">
      <label :for="`${fieldPrefix}-from`" class="block text-sm font-medium text-gray-700 mb-2">
        From Email {{ required.includes('from') ? '*' : '' }}
      </label>
      <input
        :id="`${fieldPrefix}-from`"
        :value="modelValue.from"
        @input="updateField('from', ($event.target as HTMLInputElement).value)"
        type="email"
        :required="required.includes('from')"
        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
        :placeholder="placeholders.from || '<EMAIL>'"
      />
      <p v-if="errors.from" class="mt-1 text-sm text-red-600">{{ errors.from }}</p>
    </div>

    <!-- To Field -->
    <div v-if="showField('to')">
      <label :for="`${fieldPrefix}-to`" class="block text-sm font-medium text-gray-700 mb-2">
        To Email {{ required.includes('to') ? '*' : '' }}
      </label>
      <input
        :id="`${fieldPrefix}-to`"
        :value="modelValue.to"
        @input="updateField('to', ($event.target as HTMLInputElement).value)"
        type="email"
        :required="required.includes('to')"
        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
        :placeholder="placeholders.to || '<EMAIL>'"
      />
      <p v-if="errors.to" class="mt-1 text-sm text-red-600">{{ errors.to }}</p>
    </div>

    <!-- Subject Field -->
    <div v-if="showField('subject')">
      <label :for="`${fieldPrefix}-subject`" class="block text-sm font-medium text-gray-700 mb-2">
        Subject {{ required.includes('subject') ? '*' : '' }}
      </label>
      <input
        :id="`${fieldPrefix}-subject`"
        :value="modelValue.subject"
        @input="updateField('subject', ($event.target as HTMLInputElement).value)"
        type="text"
        :required="required.includes('subject')"
        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
        :placeholder="placeholders.subject || 'Enter email subject'"
      />
      <p v-if="errors.subject" class="mt-1 text-sm text-red-600">{{ errors.subject }}</p>
    </div>

    <!-- Priority Field -->
    <div v-if="showField('priority')">
      <label :for="`${fieldPrefix}-priority`" class="block text-sm font-medium text-gray-700 mb-2">
        Priority {{ required.includes('priority') ? '*' : '' }}
      </label>
      <select
        :id="`${fieldPrefix}-priority`"
        :value="modelValue.priority"
        @change="updateField('priority', ($event.target as HTMLSelectElement).value)"
        :required="required.includes('priority')"
        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
      >
        <option value="Low">Low</option>
        <option value="Medium">Medium</option>
        <option value="High">High</option>
      </select>
      <p v-if="errors.priority" class="mt-1 text-sm text-red-600">{{ errors.priority }}</p>
    </div>

    <!-- Content Field -->
    <div v-if="showField('content')">
      <label :for="`${fieldPrefix}-content`" class="block text-sm font-medium text-gray-700 mb-2">
        Email Content {{ required.includes('content') ? '*' : '' }}
      </label>
      <textarea
        v-if="!useRichEditor"
        :id="`${fieldPrefix}-content`"
        :value="modelValue.content"
        @input="updateField('content', ($event.target as HTMLTextAreaElement).value)"
        :required="required.includes('content')"
        :rows="contentRows"
        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors resize-none"
        :placeholder="placeholders.content || 'Enter email content...'"
      ></textarea>
      
      <!-- Rich Text Editor Placeholder -->
      <div v-else class="border border-gray-300 rounded-lg p-4 bg-gray-50">
        <p class="text-sm text-gray-500">Rich text editor will be implemented here</p>
        <textarea
          :id="`${fieldPrefix}-content`"
          :value="modelValue.content"
          @input="updateField('content', ($event.target as HTMLTextAreaElement).value)"
          :required="required.includes('content')"
          :rows="contentRows"
          class="w-full mt-2 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors resize-none"
          :placeholder="placeholders.content || 'Enter email content...'"
        ></textarea>
      </div>
      
      <p v-if="errors.content" class="mt-1 text-sm text-red-600">{{ errors.content }}</p>
    </div>

    <!-- Tags Field -->
    <div v-if="showField('tags')">
      <label :for="`${fieldPrefix}-tags`" class="block text-sm font-medium text-gray-700 mb-2">
        Tags {{ required.includes('tags') ? '*' : '' }}
      </label>
      <input
        :id="`${fieldPrefix}-tags`"
        :value="tagsString"
        @input="updateTags(($event.target as HTMLInputElement).value)"
        type="text"
        :required="required.includes('tags')"
        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
        :placeholder="placeholders.tags || 'Enter tags separated by commas'"
      />
      <p class="mt-1 text-xs text-gray-500">Separate multiple tags with commas</p>
      <p v-if="errors.tags" class="mt-1 text-sm text-red-600">{{ errors.tags }}</p>
    </div>

    <!-- Assigned To Field -->
    <div v-if="showField('assignedTo')">
      <label :for="`${fieldPrefix}-assignedTo`" class="block text-sm font-medium text-gray-700 mb-2">
        Assign To {{ required.includes('assignedTo') ? '*' : '' }}
      </label>
      <select
        :id="`${fieldPrefix}-assignedTo`"
        :value="modelValue.assignedTo"
        @change="updateField('assignedTo', ($event.target as HTMLSelectElement).value)"
        :required="required.includes('assignedTo')"
        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
      >
        <option value="">Unassigned</option>
        <option v-for="member in teamMembers" :key="member.id" :value="member.id">
          {{ member.name }}
        </option>
      </select>
      <p v-if="errors.assignedTo" class="mt-1 text-sm text-red-600">{{ errors.assignedTo }}</p>
    </div>

    <!-- Attachments Field -->
    <div v-if="showField('attachments')">
      <label :for="`${fieldPrefix}-attachments`" class="block text-sm font-medium text-gray-700 mb-2">
        Attachments {{ required.includes('attachments') ? '*' : '' }}
      </label>
      <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
        <input
          :id="`${fieldPrefix}-attachments`"
          type="file"
          multiple
          @change="handleFileUpload"
          class="hidden"
          ref="fileInput"
        />
        <button
          type="button"
          @click="$refs.fileInput?.click()"
          class="inline-flex items-center space-x-2 text-sm text-gray-600 hover:text-gray-800"
        >
          <Upload class="w-5 h-5" />
          <span>Click to upload files or drag and drop</span>
        </button>
        <p class="text-xs text-gray-500 mt-1">PNG, JPG, PDF up to 10MB each</p>
      </div>
      
      <!-- File List -->
      <div v-if="modelValue.attachments && modelValue.attachments.length > 0" class="mt-3 space-y-2">
        <div
          v-for="(file, index) in modelValue.attachments"
          :key="index"
          class="flex items-center justify-between p-2 bg-gray-50 rounded-lg"
        >
          <div class="flex items-center space-x-2">
            <Paperclip class="w-4 h-4 text-gray-400" />
            <span class="text-sm text-gray-700">{{ file.name }}</span>
            <span class="text-xs text-gray-500">({{ formatFileSize(file.size) }})</span>
          </div>
          <button
            type="button"
            @click="removeFile(index)"
            class="p-1 text-gray-400 hover:text-red-500 transition-colors"
          >
            <X class="w-4 h-4" />
          </button>
        </div>
      </div>
      
      <p v-if="errors.attachments" class="mt-1 text-sm text-red-600">{{ errors.attachments }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { Upload, Paperclip, X } from 'lucide-vue-next';

interface EmailFormData {
  from?: string;
  to?: string;
  subject?: string;
  content?: string;
  priority?: 'Low' | 'Medium' | 'High';
  tags?: string[];
  assignedTo?: string;
  attachments?: File[];
}

interface TeamMember {
  id: string;
  name: string;
}

interface Props {
  modelValue: EmailFormData;
  fields?: string[];
  required?: string[];
  placeholders?: Record<string, string>;
  fieldPrefix?: string;
  contentRows?: number;
  useRichEditor?: boolean;
  teamMembers?: TeamMember[];
  errors?: Record<string, string>;
}

interface Emits {
  (e: 'update:modelValue', value: EmailFormData): void;
}

const props = withDefaults(defineProps<Props>(), {
  fields: () => ['from', 'subject', 'content', 'priority', 'tags'],
  required: () => ['from', 'subject', 'content'],
  placeholders: () => ({}),
  fieldPrefix: 'email',
  contentRows: 6,
  useRichEditor: false,
  teamMembers: () => [],
  errors: () => ({})
});

const emit = defineEmits<Emits>();

const fileInput = ref<HTMLInputElement>();

// Computed
const tagsString = computed(() => {
  return props.modelValue.tags?.join(', ') || '';
});

// Methods
const showField = (fieldName: string): boolean => {
  return props.fields.includes(fieldName);
};

const updateField = (field: keyof EmailFormData, value: any) => {
  emit('update:modelValue', {
    ...props.modelValue,
    [field]: value
  });
};

const updateTags = (value: string) => {
  const tags = value
    .split(',')
    .map(tag => tag.trim())
    .filter(tag => tag.length > 0);
  
  updateField('tags', tags);
};

const handleFileUpload = (event: Event) => {
  const target = event.target as HTMLInputElement;
  const files = Array.from(target.files || []);
  
  updateField('attachments', [
    ...(props.modelValue.attachments || []),
    ...files
  ]);
};

const removeFile = (index: number) => {
  const attachments = [...(props.modelValue.attachments || [])];
  attachments.splice(index, 1);
  updateField('attachments', attachments);
};

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};
</script>
