<template>
  <div
    v-if="isOpen"
    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
    @click.self="closeModal"
  >
    <div class="bg-white rounded-lg shadow-xl w-full max-w-3xl mx-4 max-h-[90vh] overflow-hidden">
      <!-- Header -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200">
        <div class="flex items-center space-x-3">
          <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
            <Upload class="w-4 h-4 text-green-600" />
          </div>
          <div>
            <h2 class="text-lg font-semibold text-gray-900">Import Contacts</h2>
            <p class="text-sm text-gray-500">Import to {{ columnTitle }}</p>
          </div>
        </div>
        <button
          @click="closeModal"
          class="p-2 hover:bg-gray-100 rounded-lg transition-colors"
        >
          <X class="w-5 h-5 text-gray-400" />
        </button>
      </div>

      <!-- Content -->
      <div class="p-6 space-y-6 max-h-[calc(90vh-200px)] overflow-y-auto">
        <!-- Import Method Selection -->
        <div>
          <h3 class="text-sm font-medium text-gray-700 mb-3">Import Method</h3>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button
              v-for="method in importMethods"
              :key="method.id"
              @click="selectedMethod = method.id"
              :class="[
                'p-4 border-2 rounded-lg text-left transition-all',
                selectedMethod === method.id
                  ? 'border-green-500 bg-green-50'
                  : 'border-gray-200 hover:border-gray-300'
              ]"
            >
              <div class="flex items-center space-x-3 mb-2">
                <component :is="method.icon" class="w-5 h-5 text-green-600" />
                <span class="font-medium text-gray-900">{{ method.name }}</span>
              </div>
              <p class="text-sm text-gray-600">{{ method.description }}</p>
            </button>
          </div>
        </div>

        <!-- CSV Upload -->
        <div v-if="selectedMethod === 'csv'">
          <h3 class="text-sm font-medium text-gray-700 mb-3">Upload CSV File</h3>
          <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
            <input
              ref="csvFileInput"
              type="file"
              accept=".csv,.txt"
              @change="handleFileUpload"
              class="hidden"
            />
            <Upload class="w-8 h-8 text-gray-400 mx-auto mb-2" />
            <p class="text-sm text-gray-600 mb-2">
              <button
                @click="$refs.csvFileInput?.click()"
                class="text-green-600 hover:text-green-700 font-medium"
              >
                Click to upload
              </button>
              or drag and drop
            </p>
            <p class="text-xs text-gray-500">CSV files with columns: name, email, company, phone, notes</p>
          </div>
          
          <!-- File Info -->
          <div v-if="selectedFile" class="mt-4 p-4 bg-gray-50 rounded-lg">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-2">
                <FileText class="w-4 h-4 text-gray-500" />
                <span class="text-sm font-medium">{{ selectedFile.name }}</span>
                <span class="text-xs text-gray-500">({{ formatFileSize(selectedFile.size) }})</span>
              </div>
              <button
                @click="removeFile"
                class="text-red-500 hover:text-red-700 text-sm"
              >
                Remove
              </button>
            </div>
          </div>
        </div>

        <!-- Text Input -->
        <div v-if="selectedMethod === 'text'">
          <h3 class="text-sm font-medium text-gray-700 mb-3">Paste Contact Data</h3>
          <textarea
            v-model="textInput"
            rows="8"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
            placeholder="Paste contact data here. Format: Name | <EMAIL> | Company | Phone | Notes (one per line)"
          ></textarea>
          <p class="text-xs text-gray-500 mt-2">
            Format: John Doe | <EMAIL> | Acme Corp | +1234567890 | Notes about contact
          </p>
        </div>

        <!-- Manual Entry -->
        <div v-if="selectedMethod === 'manual'">
          <h3 class="text-sm font-medium text-gray-700 mb-3">Add Multiple Contacts</h3>
          <div class="space-y-4">
            <div
              v-for="(contact, index) in manualContacts"
              :key="index"
              class="p-4 border border-gray-200 rounded-lg space-y-3"
            >
              <div class="flex items-center justify-between">
                <span class="text-sm font-medium text-gray-700">Contact {{ index + 1 }}</span>
                <button
                  v-if="manualContacts.length > 1"
                  @click="removeManualContact(index)"
                  class="text-red-500 hover:text-red-700"
                >
                  <X class="w-4 h-4" />
                </button>
              </div>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                <input
                  v-model="contact.name"
                  type="text"
                  placeholder="Contact name"
                  class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                />
                <input
                  v-model="contact.email"
                  type="email"
                  placeholder="Email address"
                  class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                />
                <input
                  v-model="contact.company"
                  type="text"
                  placeholder="Company"
                  class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                />
                <input
                  v-model="contact.phone"
                  type="tel"
                  placeholder="Phone number"
                  class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                />
              </div>
              <textarea
                v-model="contact.notes"
                rows="2"
                placeholder="Notes about contact"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
              ></textarea>
              <select
                v-model="contact.priority"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
              >
                <option value="Low">Low Priority</option>
                <option value="Medium">Medium Priority</option>
                <option value="High">High Priority</option>
              </select>
            </div>
            
            <button
              @click="addManualContact"
              class="w-full p-3 border-2 border-dashed border-gray-300 rounded-lg text-gray-600 hover:border-green-300 hover:text-green-600 transition-colors flex items-center justify-center space-x-2"
            >
              <Plus class="w-4 h-4" />
              <span>Add Another Contact</span>
            </button>
          </div>
        </div>

        <!-- Preview -->
        <div v-if="previewEmails.length > 0">
          <h3 class="text-sm font-medium text-gray-700 mb-3">Preview ({{ previewContacts.length }} contacts)</h3>
          <div class="max-h-48 overflow-y-auto border border-gray-200 rounded-lg">
            <div
              v-for="(contact, index) in previewContacts.slice(0, 5)"
              :key="index"
              class="p-3 border-b border-gray-100 last:border-b-0"
            >
              <div class="flex items-center justify-between mb-1">
                <span class="text-sm font-medium text-gray-900">{{ contact.name }}</span>
                <span class="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">{{ contact.priority }}</span>
              </div>
              <p class="text-xs text-gray-600">Email: {{ contact.email }}</p>
              <p class="text-xs text-gray-500">Company: {{ contact.company }}</p>
              <p class="text-xs text-gray-500 mt-1 truncate">{{ contact.notes }}</p>
            </div>
            <div v-if="previewContacts.length > 5" class="p-3 text-center text-sm text-gray-500">
              ... and {{ previewContacts.length - 5 }} more contacts
            </div>
          </div>
        </div>
      </div>

      <!-- Footer -->
      <div class="flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50">
        <div class="text-sm text-gray-600">
          <span v-if="previewContacts.length > 0">{{ previewContacts.length }} contacts ready to import</span>
        </div>
        <div class="flex items-center space-x-3">
          <button
            @click="closeModal"
            class="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          <button
            @click="handleImport"
            :disabled="previewContacts.length === 0 || isImporting"
            class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
          >
            <Loader2 v-if="isImporting" class="w-4 h-4 animate-spin" />
            <Upload v-else class="w-4 h-4" />
            <span>{{ isImporting ? 'Importing...' : 'Import Contacts' }}</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { Upload, X, FileText, Plus, Loader2 } from 'lucide-vue-next';

interface ContactData {
  name: string;
  email: string;
  company: string;
  phone: string;
  notes: string;
  priority: 'Low' | 'Medium' | 'High';
}

interface Props {
  isOpen: boolean;
  columnId: string;
  columnTitle: string;
}

interface Emits {
  (e: 'close'): void;
  (e: 'import', contacts: ContactData[]): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// State
const selectedMethod = ref('csv');
const selectedFile = ref<File | null>(null);
const textInput = ref('');
const manualContacts = ref<ContactData[]>([
  { name: '', email: '', company: '', phone: '', notes: '', priority: 'Medium' }
]);
const isImporting = ref(false);
const csvFileInput = ref<HTMLInputElement>();

// Import methods
const importMethods = [
  {
    id: 'csv',
    name: 'CSV File',
    description: 'Upload a CSV file with email data',
    icon: FileText
  },
  {
    id: 'text',
    name: 'Text Input',
    description: 'Paste formatted email data',
    icon: FileText
  },
  {
    id: 'manual',
    name: 'Manual Entry',
    description: 'Add emails one by one',
    icon: Plus
  }
];

// Computed
const previewContacts = computed(() => {
  if (selectedMethod.value === 'manual') {
    return manualContacts.value.filter(contact =>
      contact.name && contact.email
    );
  }

  if (selectedMethod.value === 'text' && textInput.value) {
    return parseTextInput(textInput.value);
  }

  // CSV parsing would be handled in file upload
  return [];
});

// Methods
const handleFileUpload = (event: Event) => {
  const target = event.target as HTMLInputElement;
  const file = target.files?.[0];
  if (file) {
    selectedFile.value = file;
    parseCSVFile(file);
  }
};

const parseCSVFile = async (file: File) => {
  // Simple CSV parsing - in real app, use a proper CSV parser
  const text = await file.text();
  const lines = text.split('\n').filter(line => line.trim());
  
  // Skip header if present
  const dataLines = lines.slice(1);
  
  // Parse each line (assuming CSV format: from,subject,content,priority)
  const emails = dataLines.map(line => {
    const [from, subject, content, priority] = line.split(',').map(item => item.trim());
    return {
      from: from || '',
      subject: subject || '',
      content: content || '',
      priority: (priority as 'Low' | 'Medium' | 'High') || 'Medium'
    };
  }).filter(email => email.from && email.subject);
  
  console.log('Parsed CSV emails:', emails);
};

const parseTextInput = (text: string): ContactData[] => {
  const lines = text.split('\n').filter(line => line.trim());

  return lines.map(line => {
    const [name, email, company, phone, notes] = line.split('|').map(item => item.trim());
    return {
      name: name || '',
      email: email || '',
      company: company || '',
      phone: phone || '',
      notes: notes || '',
      priority: 'Medium' as 'Low' | 'Medium' | 'High'
    };
  }).filter(contact => contact.name && contact.email);
};

const addManualContact = () => {
  manualContacts.value.push({
    name: '',
    email: '',
    company: '',
    phone: '',
    notes: '',
    priority: 'Medium'
  });
};

const removeManualContact = (index: number) => {
  manualContacts.value.splice(index, 1);
};

const removeFile = () => {
  selectedFile.value = null;
  if (csvFileInput.value) {
    csvFileInput.value.value = '';
  }
};

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

const handleImport = async () => {
  if (previewContacts.value.length === 0) return;

  isImporting.value = true;

  try {
    emit('import', previewContacts.value);
    resetForm();
  } catch (error) {
    console.error('Error importing contacts:', error);
  } finally {
    isImporting.value = false;
  }
};

const resetForm = () => {
  selectedMethod.value = 'csv';
  selectedFile.value = null;
  textInput.value = '';
  manualContacts.value = [
    { name: '', email: '', company: '', phone: '', notes: '', priority: 'Medium' }
  ];
};

const closeModal = () => {
  resetForm();
  emit('close');
};

// Watch for modal open/close
watch(() => props.isOpen, (isOpen) => {
  if (!isOpen) {
    resetForm();
  }
});
</script>
