<template>
  <div
    v-if="isOpen"
    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
    @click.self="closeModal"
  >
    <div class="bg-white rounded-lg shadow-xl w-full max-w-3xl mx-4 max-h-[90vh] overflow-hidden">
      <!-- Header -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200">
        <div class="flex items-center space-x-3">
          <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
            <Upload class="w-4 h-4 text-green-600" />
          </div>
          <div>
            <h2 class="text-lg font-semibold text-gray-900">Import Emails</h2>
            <p class="text-sm text-gray-500">Import to {{ columnTitle }}</p>
          </div>
        </div>
        <button
          @click="closeModal"
          class="p-2 hover:bg-gray-100 rounded-lg transition-colors"
        >
          <X class="w-5 h-5 text-gray-400" />
        </button>
      </div>

      <!-- Content -->
      <div class="p-6 space-y-6 max-h-[calc(90vh-200px)] overflow-y-auto">
        <!-- Import Method Selection -->
        <div>
          <h3 class="text-sm font-medium text-gray-700 mb-3">Import Method</h3>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button
              v-for="method in importMethods"
              :key="method.id"
              @click="selectedMethod = method.id"
              :class="[
                'p-4 border-2 rounded-lg text-left transition-all',
                selectedMethod === method.id
                  ? 'border-green-500 bg-green-50'
                  : 'border-gray-200 hover:border-gray-300'
              ]"
            >
              <div class="flex items-center space-x-3 mb-2">
                <component :is="method.icon" class="w-5 h-5 text-green-600" />
                <span class="font-medium text-gray-900">{{ method.name }}</span>
              </div>
              <p class="text-sm text-gray-600">{{ method.description }}</p>
            </button>
          </div>
        </div>

        <!-- CSV Upload -->
        <div v-if="selectedMethod === 'csv'">
          <h3 class="text-sm font-medium text-gray-700 mb-3">Upload CSV File</h3>
          <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
            <input
              ref="csvFileInput"
              type="file"
              accept=".csv,.txt"
              @change="handleFileUpload"
              class="hidden"
            />
            <Upload class="w-8 h-8 text-gray-400 mx-auto mb-2" />
            <p class="text-sm text-gray-600 mb-2">
              <button
                @click="$refs.csvFileInput?.click()"
                class="text-green-600 hover:text-green-700 font-medium"
              >
                Click to upload
              </button>
              or drag and drop
            </p>
            <p class="text-xs text-gray-500">CSV files with columns: from, subject, content, priority</p>
          </div>
          
          <!-- File Info -->
          <div v-if="selectedFile" class="mt-4 p-4 bg-gray-50 rounded-lg">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-2">
                <FileText class="w-4 h-4 text-gray-500" />
                <span class="text-sm font-medium">{{ selectedFile.name }}</span>
                <span class="text-xs text-gray-500">({{ formatFileSize(selectedFile.size) }})</span>
              </div>
              <button
                @click="removeFile"
                class="text-red-500 hover:text-red-700 text-sm"
              >
                Remove
              </button>
            </div>
          </div>
        </div>

        <!-- Text Input -->
        <div v-if="selectedMethod === 'text'">
          <h3 class="text-sm font-medium text-gray-700 mb-3">Paste Email Data</h3>
          <textarea
            v-model="textInput"
            rows="8"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
            placeholder="Paste email data here. Format: <EMAIL> | Subject | Content | Priority (one per line)"
          ></textarea>
          <p class="text-xs text-gray-500 mt-2">
            Format: <EMAIL> | Subject line | Email content | High/Medium/Low
          </p>
        </div>

        <!-- Manual Entry -->
        <div v-if="selectedMethod === 'manual'">
          <h3 class="text-sm font-medium text-gray-700 mb-3">Add Multiple Emails</h3>
          <div class="space-y-4">
            <div
              v-for="(email, index) in manualEmails"
              :key="index"
              class="p-4 border border-gray-200 rounded-lg space-y-3"
            >
              <div class="flex items-center justify-between">
                <span class="text-sm font-medium text-gray-700">Email {{ index + 1 }}</span>
                <button
                  v-if="manualEmails.length > 1"
                  @click="removeManualEmail(index)"
                  class="text-red-500 hover:text-red-700"
                >
                  <X class="w-4 h-4" />
                </button>
              </div>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                <input
                  v-model="email.from"
                  type="email"
                  placeholder="From email"
                  class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                />
                <input
                  v-model="email.subject"
                  type="text"
                  placeholder="Subject"
                  class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                />
              </div>
              <textarea
                v-model="email.content"
                rows="3"
                placeholder="Email content"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
              ></textarea>
              <select
                v-model="email.priority"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
              >
                <option value="Low">Low Priority</option>
                <option value="Medium">Medium Priority</option>
                <option value="High">High Priority</option>
              </select>
            </div>
            
            <button
              @click="addManualEmail"
              class="w-full p-3 border-2 border-dashed border-gray-300 rounded-lg text-gray-600 hover:border-green-300 hover:text-green-600 transition-colors flex items-center justify-center space-x-2"
            >
              <Plus class="w-4 h-4" />
              <span>Add Another Email</span>
            </button>
          </div>
        </div>

        <!-- Preview -->
        <div v-if="previewEmails.length > 0">
          <h3 class="text-sm font-medium text-gray-700 mb-3">Preview ({{ previewEmails.length }} emails)</h3>
          <div class="max-h-48 overflow-y-auto border border-gray-200 rounded-lg">
            <div
              v-for="(email, index) in previewEmails.slice(0, 5)"
              :key="index"
              class="p-3 border-b border-gray-100 last:border-b-0"
            >
              <div class="flex items-center justify-between mb-1">
                <span class="text-sm font-medium text-gray-900">{{ email.subject }}</span>
                <span class="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">{{ email.priority }}</span>
              </div>
              <p class="text-xs text-gray-600">From: {{ email.from }}</p>
              <p class="text-xs text-gray-500 mt-1 truncate">{{ email.content }}</p>
            </div>
            <div v-if="previewEmails.length > 5" class="p-3 text-center text-sm text-gray-500">
              ... and {{ previewEmails.length - 5 }} more emails
            </div>
          </div>
        </div>
      </div>

      <!-- Footer -->
      <div class="flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50">
        <div class="text-sm text-gray-600">
          <span v-if="previewEmails.length > 0">{{ previewEmails.length }} emails ready to import</span>
        </div>
        <div class="flex items-center space-x-3">
          <button
            @click="closeModal"
            class="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          <button
            @click="handleImport"
            :disabled="previewEmails.length === 0 || isImporting"
            class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
          >
            <Loader2 v-if="isImporting" class="w-4 h-4 animate-spin" />
            <Upload v-else class="w-4 h-4" />
            <span>{{ isImporting ? 'Importing...' : 'Import Emails' }}</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { Upload, X, FileText, Plus, Loader2 } from 'lucide-vue-next';

interface EmailData {
  from: string;
  subject: string;
  content: string;
  priority: 'Low' | 'Medium' | 'High';
}

interface Props {
  isOpen: boolean;
  columnId: string;
  columnTitle: string;
}

interface Emits {
  (e: 'close'): void;
  (e: 'import', emails: EmailData[]): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// State
const selectedMethod = ref('csv');
const selectedFile = ref<File | null>(null);
const textInput = ref('');
const manualEmails = ref<EmailData[]>([
  { from: '', subject: '', content: '', priority: 'Medium' }
]);
const isImporting = ref(false);
const csvFileInput = ref<HTMLInputElement>();

// Import methods
const importMethods = [
  {
    id: 'csv',
    name: 'CSV File',
    description: 'Upload a CSV file with email data',
    icon: FileText
  },
  {
    id: 'text',
    name: 'Text Input',
    description: 'Paste formatted email data',
    icon: FileText
  },
  {
    id: 'manual',
    name: 'Manual Entry',
    description: 'Add emails one by one',
    icon: Plus
  }
];

// Computed
const previewEmails = computed(() => {
  if (selectedMethod.value === 'manual') {
    return manualEmails.value.filter(email => 
      email.from && email.subject && email.content
    );
  }
  
  if (selectedMethod.value === 'text' && textInput.value) {
    return parseTextInput(textInput.value);
  }
  
  // CSV parsing would be handled in file upload
  return [];
});

// Methods
const handleFileUpload = (event: Event) => {
  const target = event.target as HTMLInputElement;
  const file = target.files?.[0];
  if (file) {
    selectedFile.value = file;
    parseCSVFile(file);
  }
};

const parseCSVFile = async (file: File) => {
  // Simple CSV parsing - in real app, use a proper CSV parser
  const text = await file.text();
  const lines = text.split('\n').filter(line => line.trim());
  
  // Skip header if present
  const dataLines = lines.slice(1);
  
  // Parse each line (assuming CSV format: from,subject,content,priority)
  const emails = dataLines.map(line => {
    const [from, subject, content, priority] = line.split(',').map(item => item.trim());
    return {
      from: from || '',
      subject: subject || '',
      content: content || '',
      priority: (priority as 'Low' | 'Medium' | 'High') || 'Medium'
    };
  }).filter(email => email.from && email.subject);
  
  console.log('Parsed CSV emails:', emails);
};

const parseTextInput = (text: string): EmailData[] => {
  const lines = text.split('\n').filter(line => line.trim());
  
  return lines.map(line => {
    const [from, subject, content, priority] = line.split('|').map(item => item.trim());
    return {
      from: from || '',
      subject: subject || '',
      content: content || '',
      priority: (priority as 'Low' | 'Medium' | 'High') || 'Medium'
    };
  }).filter(email => email.from && email.subject);
};

const addManualEmail = () => {
  manualEmails.value.push({
    from: '',
    subject: '',
    content: '',
    priority: 'Medium'
  });
};

const removeManualEmail = (index: number) => {
  manualEmails.value.splice(index, 1);
};

const removeFile = () => {
  selectedFile.value = null;
  if (csvFileInput.value) {
    csvFileInput.value.value = '';
  }
};

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

const handleImport = async () => {
  if (previewEmails.value.length === 0) return;
  
  isImporting.value = true;
  
  try {
    emit('import', previewEmails.value);
    resetForm();
  } catch (error) {
    console.error('Error importing emails:', error);
  } finally {
    isImporting.value = false;
  }
};

const resetForm = () => {
  selectedMethod.value = 'csv';
  selectedFile.value = null;
  textInput.value = '';
  manualEmails.value = [
    { from: '', subject: '', content: '', priority: 'Medium' }
  ];
};

const closeModal = () => {
  resetForm();
  emit('close');
};

// Watch for modal open/close
watch(() => props.isOpen, (isOpen) => {
  if (!isOpen) {
    resetForm();
  }
});
</script>
