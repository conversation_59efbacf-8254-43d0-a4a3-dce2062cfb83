<template>
  <div
    v-if="isOpen"
    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
    @click.self="closeModal"
  >
    <div class="bg-white rounded-lg shadow-xl w-full max-w-2xl mx-4 max-h-[90vh] overflow-hidden">
      <!-- Header -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200">
        <div class="flex items-center space-x-3">
          <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
            <Zap class="w-4 h-4 text-yellow-600" />
          </div>
          <div>
            <h2 class="text-lg font-semibold text-gray-900">Email Actions</h2>
            <p class="text-sm text-gray-500">{{ email?.subject || 'No email selected' }}</p>
          </div>
        </div>
        <button
          @click="closeModal"
          class="p-2 hover:bg-gray-100 rounded-lg transition-colors"
        >
          <X class="w-5 h-5 text-gray-400" />
        </button>
      </div>

      <!-- Content -->
      <div class="p-6 space-y-6 max-h-[calc(90vh-200px)] overflow-y-auto">
        <!-- Quick Actions -->
        <div>
          <h3 class="text-sm font-medium text-gray-700 mb-4">Quick Actions</h3>
          <div class="grid grid-cols-2 gap-3">
            <button
              v-for="action in quickActions"
              :key="action.id"
              @click="handleQuickAction(action.id)"
              :class="[
                'p-4 border border-gray-200 rounded-lg text-left hover:border-blue-300 hover:bg-blue-50 transition-all group',
                action.color === 'red' ? 'hover:border-red-300 hover:bg-red-50' : '',
                action.color === 'green' ? 'hover:border-green-300 hover:bg-green-50' : '',
                action.color === 'yellow' ? 'hover:border-yellow-300 hover:bg-yellow-50' : ''
              ]"
            >
              <div class="flex items-center space-x-3">
                <component 
                  :is="action.icon" 
                  :class="[
                    'w-5 h-5',
                    action.color === 'red' ? 'text-red-600' : '',
                    action.color === 'green' ? 'text-green-600' : '',
                    action.color === 'yellow' ? 'text-yellow-600' : '',
                    action.color === 'blue' ? 'text-blue-600' : 'text-gray-600'
                  ]"
                />
                <div>
                  <p class="font-medium text-gray-900">{{ action.name }}</p>
                  <p class="text-xs text-gray-500">{{ action.description }}</p>
                </div>
              </div>
            </button>
          </div>
        </div>

        <!-- Response Actions -->
        <div>
          <h3 class="text-sm font-medium text-gray-700 mb-4">Response Actions</h3>
          <div class="space-y-3">
            <button
              v-for="response in responseActions"
              :key="response.id"
              @click="handleResponseAction(response.id)"
              class="w-full p-4 border border-gray-200 rounded-lg text-left hover:border-blue-300 hover:bg-blue-50 transition-all flex items-center justify-between"
            >
              <div class="flex items-center space-x-3">
                <component :is="response.icon" class="w-5 h-5 text-blue-600" />
                <div>
                  <p class="font-medium text-gray-900">{{ response.name }}</p>
                  <p class="text-sm text-gray-500">{{ response.description }}</p>
                </div>
              </div>
              <ChevronRight class="w-4 h-4 text-gray-400" />
            </button>
          </div>
        </div>

        <!-- Automation Actions -->
        <div>
          <h3 class="text-sm font-medium text-gray-700 mb-4">Automation</h3>
          <div class="space-y-3">
            <button
              v-for="automation in automationActions"
              :key="automation.id"
              @click="handleAutomationAction(automation.id)"
              class="w-full p-4 border border-gray-200 rounded-lg text-left hover:border-purple-300 hover:bg-purple-50 transition-all flex items-center justify-between"
            >
              <div class="flex items-center space-x-3">
                <component :is="automation.icon" class="w-5 h-5 text-purple-600" />
                <div>
                  <p class="font-medium text-gray-900">{{ automation.name }}</p>
                  <p class="text-sm text-gray-500">{{ automation.description }}</p>
                </div>
              </div>
              <ChevronRight class="w-4 h-4 text-gray-400" />
            </button>
          </div>
        </div>

        <!-- Advanced Actions -->
        <div>
          <h3 class="text-sm font-medium text-gray-700 mb-4">Advanced</h3>
          <div class="grid grid-cols-1 gap-3">
            <button
              v-for="advanced in advancedActions"
              :key="advanced.id"
              @click="handleAdvancedAction(advanced.id)"
              class="p-3 border border-gray-200 rounded-lg text-left hover:border-gray-300 hover:bg-gray-50 transition-all flex items-center justify-between"
            >
              <div class="flex items-center space-x-3">
                <component :is="advanced.icon" class="w-4 h-4 text-gray-600" />
                <span class="text-sm font-medium text-gray-900">{{ advanced.name }}</span>
              </div>
              <ChevronRight class="w-3 h-3 text-gray-400" />
            </button>
          </div>
        </div>
      </div>

      <!-- Footer -->
      <div class="flex items-center justify-end p-6 border-t border-gray-200 bg-gray-50">
        <button
          @click="closeModal"
          class="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
        >
          Close
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { 
  Zap, X, ChevronRight, Reply, Forward, Archive, Trash2, 
  Flag, Clock, User, Tag, Copy, Download, Share, 
  Bot, Workflow, Calendar, Bell
} from 'lucide-vue-next';

interface EmailCard {
  id: string;
  subject: string;
  from: string;
  content: string;
  priority: string;
  tags: string[];
  date: string;
  assignedTo: string;
  isRead: boolean;
}

interface Props {
  isOpen: boolean;
  email: EmailCard | null;
}

interface Emits {
  (e: 'close'): void;
  (e: 'action-created', action: { type: string; data: any }): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// Quick Actions
const quickActions = ref([
  {
    id: 'reply',
    name: 'Reply',
    description: 'Send a response',
    icon: Reply,
    color: 'blue'
  },
  {
    id: 'forward',
    name: 'Forward',
    description: 'Forward to someone',
    icon: Forward,
    color: 'blue'
  },
  {
    id: 'archive',
    name: 'Archive',
    description: 'Move to archive',
    icon: Archive,
    color: 'green'
  },
  {
    id: 'delete',
    name: 'Delete',
    description: 'Delete permanently',
    icon: Trash2,
    color: 'red'
  },
  {
    id: 'flag',
    name: 'Flag',
    description: 'Mark as important',
    icon: Flag,
    color: 'yellow'
  },
  {
    id: 'snooze',
    name: 'Snooze',
    description: 'Remind me later',
    icon: Clock,
    color: 'blue'
  }
]);

// Response Actions
const responseActions = ref([
  {
    id: 'quick-reply',
    name: 'Quick Reply',
    description: 'Use pre-written responses',
    icon: Reply
  },
  {
    id: 'template-reply',
    name: 'Template Reply',
    description: 'Reply using templates',
    icon: Copy
  },
  {
    id: 'custom-reply',
    name: 'Custom Reply',
    description: 'Write a custom response',
    icon: Reply
  }
]);

// Automation Actions
const automationActions = ref([
  {
    id: 'auto-reply',
    name: 'Auto Reply',
    description: 'Set up automatic response',
    icon: Bot
  },
  {
    id: 'create-sequence',
    name: 'Email Sequence',
    description: 'Add to email sequence',
    icon: Workflow
  },
  {
    id: 'schedule-followup',
    name: 'Schedule Follow-up',
    description: 'Create follow-up reminder',
    icon: Calendar
  },
  {
    id: 'notify-team',
    name: 'Notify Team',
    description: 'Alert team members',
    icon: Bell
  }
]);

// Advanced Actions
const advancedActions = ref([
  {
    id: 'assign',
    name: 'Assign to Team Member',
    icon: User
  },
  {
    id: 'add-tags',
    name: 'Add Tags',
    icon: Tag
  },
  {
    id: 'export',
    name: 'Export Email',
    icon: Download
  },
  {
    id: 'share',
    name: 'Share Email',
    icon: Share
  }
]);

// Methods
const handleQuickAction = (actionId: string) => {
  console.log('Quick action:', actionId, props.email);
  
  const actionData = {
    type: 'quick',
    action: actionId,
    email: props.email
  };
  
  emit('action-created', actionData);
  
  // For demo purposes, close modal after action
  if (['archive', 'delete', 'flag'].includes(actionId)) {
    closeModal();
  }
};

const handleResponseAction = (actionId: string) => {
  console.log('Response action:', actionId, props.email);
  
  const actionData = {
    type: 'response',
    action: actionId,
    email: props.email
  };
  
  emit('action-created', actionData);
  closeModal();
};

const handleAutomationAction = (actionId: string) => {
  console.log('Automation action:', actionId, props.email);
  
  const actionData = {
    type: 'automation',
    action: actionId,
    email: props.email
  };
  
  emit('action-created', actionData);
  closeModal();
};

const handleAdvancedAction = (actionId: string) => {
  console.log('Advanced action:', actionId, props.email);
  
  const actionData = {
    type: 'advanced',
    action: actionId,
    email: props.email
  };
  
  emit('action-created', actionData);
  closeModal();
};

const closeModal = () => {
  emit('close');
};
</script>
