<template>
  <div
    v-if="isOpen"
    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
    @click.self="closeModal"
  >
    <div class="bg-white rounded-lg shadow-xl w-full max-w-4xl mx-4 max-h-[90vh] overflow-hidden">
      <!-- Header -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200">
        <div class="flex items-center space-x-3">
          <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
            <Edit class="w-4 h-4 text-blue-600" />
          </div>
          <div>
            <h2 class="text-lg font-semibold text-gray-900">Create Manual Email</h2>
            <p class="text-sm text-gray-500">Add to {{ columnTitle }}</p>
          </div>
        </div>
        <button
          @click="closeModal"
          class="p-2 hover:bg-gray-100 rounded-lg transition-colors"
        >
          <X class="w-5 h-5 text-gray-400" />
        </button>
      </div>

      <!-- Form -->
      <form @submit.prevent="handleSubmit" class="flex-1 overflow-hidden">
        <div class="p-6 space-y-6 max-h-[calc(90vh-200px)] overflow-y-auto">
          <!-- Email Form Fields -->
          <EmailFormFields
            v-model="emailData"
            :fields="['from', 'to', 'subject', 'content', 'priority', 'tags', 'assignedTo', 'attachments']"
            :required="['from', 'subject', 'content']"
            :placeholders="{
              from: '<EMAIL>',
              to: '<EMAIL>',
              subject: 'Enter email subject',
              content: 'Enter detailed email content...',
              tags: 'urgent, customer-inquiry, follow-up'
            }"
            :team-members="teamMembers"
            :use-rich-editor="true"
            :content-rows="8"
            :errors="errors"
            field-prefix="manual"
          />

          <!-- Additional Options -->
          <div class="border-t border-gray-200 pt-6">
            <h3 class="text-sm font-medium text-gray-700 mb-4">Additional Options</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- Email Type -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Email Type</label>
                <select
                  v-model="emailData.type"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="inquiry">Customer Inquiry</option>
                  <option value="complaint">Complaint</option>
                  <option value="support">Support Request</option>
                  <option value="sales">Sales Lead</option>
                  <option value="feedback">Feedback</option>
                  <option value="other">Other</option>
                </select>
              </div>

              <!-- Due Date -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Due Date (optional)</label>
                <input
                  v-model="emailData.dueDate"
                  type="datetime-local"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <!-- Customer Info -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Customer Name (optional)</label>
                <input
                  v-model="emailData.customerName"
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="John Doe"
                />
              </div>

              <!-- Customer Company -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Company (optional)</label>
                <input
                  v-model="emailData.company"
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Acme Corp"
                />
              </div>
            </div>

            <!-- Notes -->
            <div class="mt-6">
              <label class="block text-sm font-medium text-gray-700 mb-2">Internal Notes (optional)</label>
              <textarea
                v-model="emailData.notes"
                rows="3"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Add internal notes that won't be visible to the customer..."
              ></textarea>
            </div>

            <!-- Auto Actions -->
            <div class="mt-6">
              <h4 class="text-sm font-medium text-gray-700 mb-3">Auto Actions</h4>
              <div class="space-y-3">
                <label class="flex items-center">
                  <input
                    v-model="emailData.autoReply"
                    type="checkbox"
                    class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span class="ml-2 text-sm text-gray-700">Send auto-reply acknowledgment</span>
                </label>
                
                <label class="flex items-center">
                  <input
                    v-model="emailData.createFollowUp"
                    type="checkbox"
                    class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span class="ml-2 text-sm text-gray-700">Create follow-up reminder</span>
                </label>
                
                <label class="flex items-center">
                  <input
                    v-model="emailData.notifyTeam"
                    type="checkbox"
                    class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span class="ml-2 text-sm text-gray-700">Notify team members</span>
                </label>
              </div>
            </div>
          </div>
        </div>

        <!-- Footer -->
        <div class="flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50">
          <div class="flex items-center space-x-3">
            <button
              type="button"
              @click="saveAsDraft"
              class="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Save as Draft
            </button>
          </div>
          
          <div class="flex items-center space-x-3">
            <button
              type="button"
              @click="closeModal"
              class="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              :disabled="isSubmitting"
              class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
            >
              <Loader2 v-if="isSubmitting" class="w-4 h-4 animate-spin" />
              <Edit v-else class="w-4 h-4" />
              <span>{{ isSubmitting ? 'Creating...' : 'Create Email' }}</span>
            </button>
          </div>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { Edit, X, Loader2 } from 'lucide-vue-next';
// import EmailFormFields from './EmailFormFields.vue';

interface EmailFormData {
  from: string;
  to: string;
  subject: string;
  content: string;
  priority: 'Low' | 'Medium' | 'High';
  tags: string[];
  assignedTo: string;
  attachments: File[];
  type: string;
  dueDate: string;
  customerName: string;
  company: string;
  notes: string;
  autoReply: boolean;
  createFollowUp: boolean;
  notifyTeam: boolean;
}

interface Props {
  isOpen: boolean;
  columnId: string;
  columnTitle: string;
}

interface Emits {
  (e: 'close'): void;
  (e: 'create', email: EmailFormData & { columnId: string }): void;
  (e: 'save-draft', email: EmailFormData & { columnId: string }): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// Form state
const emailData = ref<EmailFormData>({
  from: '',
  to: '',
  subject: '',
  content: '',
  priority: 'Medium',
  tags: [],
  assignedTo: '',
  attachments: [],
  type: 'inquiry',
  dueDate: '',
  customerName: '',
  company: '',
  notes: '',
  autoReply: false,
  createFollowUp: false,
  notifyTeam: false
});

const isSubmitting = ref(false);
const errors = ref<Record<string, string>>({});

// Mock team members - in real app, this would come from props or store
const teamMembers = ref([
  { id: '1', name: 'John Smith' },
  { id: '2', name: 'Sarah Johnson' },
  { id: '3', name: 'Mike Wilson' },
  { id: '4', name: 'Lisa Chen' }
]);

// Methods
const validateForm = (): boolean => {
  errors.value = {};
  
  if (!emailData.value.from.trim()) {
    errors.value.from = 'From email is required';
  }
  
  if (!emailData.value.subject.trim()) {
    errors.value.subject = 'Subject is required';
  }
  
  if (!emailData.value.content.trim()) {
    errors.value.content = 'Email content is required';
  }
  
  return Object.keys(errors.value).length === 0;
};

const handleSubmit = async () => {
  if (!validateForm()) return;
  
  isSubmitting.value = true;
  
  try {
    const emailWithColumn = {
      ...emailData.value,
      columnId: props.columnId
    };
    
    emit('create', emailWithColumn);
    resetForm();
  } catch (error) {
    console.error('Error creating email:', error);
  } finally {
    isSubmitting.value = false;
  }
};

const saveAsDraft = () => {
  const emailWithColumn = {
    ...emailData.value,
    columnId: props.columnId
  };
  
  emit('save-draft', emailWithColumn);
  resetForm();
};

const resetForm = () => {
  emailData.value = {
    from: '',
    to: '',
    subject: '',
    content: '',
    priority: 'Medium',
    tags: [],
    assignedTo: '',
    attachments: [],
    type: 'inquiry',
    dueDate: '',
    customerName: '',
    company: '',
    notes: '',
    autoReply: false,
    createFollowUp: false,
    notifyTeam: false
  };
  errors.value = {};
};

const closeModal = () => {
  resetForm();
  emit('close');
};

// Watch for modal open/close
watch(() => props.isOpen, (isOpen) => {
  if (!isOpen) {
    resetForm();
  }
});
</script>
