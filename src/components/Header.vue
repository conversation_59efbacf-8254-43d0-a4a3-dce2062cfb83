<template>
  <header class="bg-white border-b border-gray-200 px-6 py-4">
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-4">
        <div class="flex items-center space-x-2">
          <div class="w-8 h-8 bg-gradient-to-r from-blue-600 to-teal-600 rounded-lg flex items-center justify-center">
            <Mail class="w-5 h-5 text-white" />
          </div>
          <h1 class="text-2xl font-bold text-gray-900">KirimPintar</h1>
          <span class="bg-gradient-to-r from-blue-600 to-teal-600 text-white text-xs px-2 py-1 rounded-full">
            AI
          </span>
        </div>
      </div>

      <div class="flex-1 max-w-lg mx-8">
        <div class="relative">
          <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="Cari email, pelanggan, atau tag..."
            class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
      </div>

      <div class="flex items-center space-x-3">
        <button
          @click="$emit('create-email')"
          class="bg-gradient-to-r from-blue-600 to-teal-600 text-white px-4 py-2 rounded-lg hover:from-blue-700 hover:to-teal-700 transition-all duration-200 flex items-center space-x-2 shadow-md hover:shadow-lg"
        >
          <Plus class="w-4 h-4" />
          <span>Email Baru</span>
        </button>
        
        <button class="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors">
          <Zap class="w-5 h-5" />
        </button>
        
        <button class="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors relative">
          <Bell class="w-5 h-5" />
          <span class="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></span>
        </button>
        
        <button class="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors">
          <Settings class="w-5 h-5" />
        </button>

        <div class="flex items-center space-x-2 ml-4">
          <img
            src="https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?auto=compress&cs=tinysrgb&w=40&h=40&dpr=2"
            alt="Profile"
            class="w-8 h-8 rounded-full"
          />
          <div class="hidden md:block">
            <p class="text-sm font-medium text-gray-900">Ahmad Wijaya</p>
            <p class="text-xs text-gray-500">Admin</p>
          </div>
        </div>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { Mail, Settings, Bell, Search, Plus, Zap } from 'lucide-vue-next';

defineEmits<{
  'create-email': [];
}>();
</script>
