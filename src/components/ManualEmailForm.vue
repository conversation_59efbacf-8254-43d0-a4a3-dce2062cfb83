<template>
  <div v-if="isOpen" class="fixed inset-0 z-50 overflow-y-auto">
    <!-- Backdrop -->
    <div 
      class="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
      @click="closeModal"
    ></div>
    
    <!-- Modal -->
    <div class="flex min-h-full items-center justify-center p-4">
      <div class="relative bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden animate-fade-in">
        <!-- Header -->
        <div class="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 class="text-xl font-semibold text-gray-900">Create Email Manually</h2>
          <button
            @click="closeModal"
            class="p-2 text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X class="w-5 h-5" />
          </button>
        </div>

        <!-- Form -->
        <div class="p-6 overflow-y-auto max-h-96">
          <form @submit.prevent="createEmail" class="space-y-6">
            <!-- Email Type -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Email Type</label>
              <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
                <label class="flex items-center space-x-3 p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                  <input
                    v-model="emailForm.type"
                    type="radio"
                    value="incoming"
                    class="text-blue-600 focus:ring-blue-500"
                  />
                  <div>
                    <div class="font-medium">Incoming Email</div>
                    <div class="text-sm text-gray-500">Customer contacted us</div>
                  </div>
                </label>
                <label class="flex items-center space-x-3 p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                  <input
                    v-model="emailForm.type"
                    type="radio"
                    value="phone"
                    class="text-blue-600 focus:ring-blue-500"
                  />
                  <div>
                    <div class="font-medium">Phone Call</div>
                    <div class="text-sm text-gray-500">Convert call to email</div>
                  </div>
                </label>
                <label class="flex items-center space-x-3 p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                  <input
                    v-model="emailForm.type"
                    type="radio"
                    value="meeting"
                    class="text-blue-600 focus:ring-blue-500"
                  />
                  <div>
                    <div class="font-medium">Meeting Notes</div>
                    <div class="text-sm text-gray-500">Convert meeting to email</div>
                  </div>
                </label>
              </div>
            </div>

            <!-- Basic Information -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">From</label>
                <input
                  v-model="emailForm.from"
                  type="email"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="<EMAIL>"
                />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">To</label>
                <input
                  v-model="emailForm.to"
                  type="email"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="<EMAIL>"
                />
              </div>
            </div>

            <!-- Subject -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Subject</label>
              <input
                v-model="emailForm.subject"
                type="text"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Enter email subject"
              />
            </div>

            <!-- Content -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Content</label>
              <textarea
                v-model="emailForm.content"
                rows="6"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Enter email content or conversation notes..."
              ></textarea>
            </div>

            <!-- Metadata -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Priority</label>
                <select
                  v-model="emailForm.priority"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="low">Low Priority</option>
                  <option value="medium">Medium Priority</option>
                  <option value="high">High Priority</option>
                </select>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Category</label>
                <select
                  v-model="emailForm.category"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="General">General</option>
                  <option value="Support">Support</option>
                  <option value="Sales">Sales</option>
                  <option value="Billing">Billing</option>
                  <option value="Technical">Technical</option>
                  <option value="Feedback">Feedback</option>
                </select>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Assign To</label>
                <select
                  v-model="emailForm.assignedTo"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Unassigned</option>
                  <option v-for="member in team" :key="member.id" :value="member.id">
                    {{ member.name }}
                  </option>
                </select>
              </div>
            </div>

            <!-- Tags -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Tags</label>
              <div class="flex flex-wrap gap-2 mb-2">
                <span
                  v-for="tag in emailForm.tags"
                  :key="tag"
                  class="inline-flex items-center px-2 py-1 bg-blue-100 text-blue-800 text-sm rounded-full"
                >
                  {{ tag }}
                  <button
                    @click="removeTag(tag)"
                    type="button"
                    class="ml-1 text-blue-600 hover:text-blue-800"
                  >
                    <X class="w-3 h-3" />
                  </button>
                </span>
              </div>
              <div class="flex space-x-2">
                <input
                  v-model="newTag"
                  type="text"
                  placeholder="Add tag"
                  class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  @keyup.enter="addTag"
                />
                <button
                  @click="addTag"
                  type="button"
                  class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                >
                  Add
                </button>
              </div>
            </div>

            <!-- Date/Time -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Date</label>
                <input
                  v-model="emailForm.date"
                  type="date"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Time</label>
                <input
                  v-model="emailForm.time"
                  type="time"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>

            <!-- Additional Options -->
            <div class="space-y-3">
              <label class="flex items-center space-x-2">
                <input
                  v-model="emailForm.isRead"
                  type="checkbox"
                  class="text-blue-600 focus:ring-blue-500 rounded"
                />
                <span class="text-sm text-gray-700">Mark as read</span>
              </label>
              <label class="flex items-center space-x-2">
                <input
                  v-model="emailForm.needsFollowUp"
                  type="checkbox"
                  class="text-blue-600 focus:ring-blue-500 rounded"
                />
                <span class="text-sm text-gray-700">Needs follow-up</span>
              </label>
            </div>

            <!-- Follow-up Date -->
            <div v-if="emailForm.needsFollowUp">
              <label class="block text-sm font-medium text-gray-700 mb-2">Follow-up Date</label>
              <input
                v-model="emailForm.followUpDate"
                type="datetime-local"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </form>
        </div>

        <!-- Footer -->
        <div class="flex items-center justify-end p-6 border-t border-gray-200 space-x-3">
          <button
            @click="closeModal"
            type="button"
            class="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          <button
            @click="createEmail"
            type="button"
            class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Create Email
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { X } from 'lucide-vue-next';
import { mockTeam } from '@/data/mockData';

const props = defineProps<{
  isOpen: boolean;
  columnId: string;
}>();

const emit = defineEmits<{
  'close': [];
  'create': [email: any, columnId: string];
}>();

const team = mockTeam;
const newTag = ref('');

const emailForm = reactive({
  type: 'incoming',
  from: '',
  to: '<EMAIL>',
  subject: '',
  content: '',
  priority: 'medium',
  category: 'General',
  assignedTo: '',
  tags: [] as string[],
  date: new Date().toISOString().split('T')[0],
  time: new Date().toTimeString().split(' ')[0].substring(0, 5),
  isRead: false,
  needsFollowUp: false,
  followUpDate: ''
});

const addTag = () => {
  if (newTag.value.trim() && !emailForm.tags.includes(newTag.value.trim())) {
    emailForm.tags.push(newTag.value.trim());
    newTag.value = '';
  }
};

const removeTag = (tag: string) => {
  const index = emailForm.tags.indexOf(tag);
  if (index > -1) {
    emailForm.tags.splice(index, 1);
  }
};

const createEmail = () => {
  const timestamp = new Date(`${emailForm.date}T${emailForm.time}`);
  
  const email = {
    subject: emailForm.subject,
    from: emailForm.from,
    to: emailForm.to,
    content: emailForm.content,
    priority: emailForm.priority,
    sentiment: 'neutral',
    category: emailForm.category,
    assignedTo: emailForm.assignedTo || undefined,
    tags: emailForm.tags,
    attachments: 0,
    isRead: emailForm.isRead,
    timestamp,
    followUpDate: emailForm.needsFollowUp ? new Date(emailForm.followUpDate) : undefined
  };

  emit('create', email, props.columnId);
  closeModal();
};

const closeModal = () => {
  // Reset form
  Object.assign(emailForm, {
    type: 'incoming',
    from: '',
    to: '<EMAIL>',
    subject: '',
    content: '',
    priority: 'medium',
    category: 'General',
    assignedTo: '',
    tags: [],
    date: new Date().toISOString().split('T')[0],
    time: new Date().toTimeString().split(' ')[0].substring(0, 5),
    isRead: false,
    needsFollowUp: false,
    followUpDate: ''
  });
  newTag.value = '';
  emit('close');
};
</script>
