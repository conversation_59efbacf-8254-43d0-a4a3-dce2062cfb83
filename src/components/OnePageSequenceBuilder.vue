<template>
  <div
    v-if="isOpen"
    class="fixed inset-0 bg-white z-50 overflow-hidden"
  >
    <!-- Header -->
    <div class="flex items-center justify-between p-6 border-b border-gray-200 bg-white">
      <div class="flex items-center space-x-4">
        <button
          @click="closeBuilder"
          class="p-2 hover:bg-gray-100 rounded-lg transition-colors"
        >
          <ArrowLeft class="w-5 h-5 text-gray-600" />
        </button>
        <div>
          <h1 class="text-xl font-semibold text-gray-900">{{ sequence.name || 'New Sequence' }}</h1>
          <p class="text-sm text-gray-500">{{ columnTitle }} - Email Sequence Builder</p>
        </div>
      </div>
      
      <div class="flex items-center space-x-3">
        <!-- Auto-save indicator -->
        <div class="flex items-center space-x-2 text-sm text-gray-500">
          <div :class="[
            'w-2 h-2 rounded-full',
            autoSaveStatus === 'saving' ? 'bg-yellow-500' :
            autoSaveStatus === 'saved' ? 'bg-green-500' : 'bg-gray-300'
          ]"></div>
          <span>{{ autoSaveText }}</span>
        </div>
        
        <!-- Sequence controls -->
        <button
          @click="toggleSequenceStatus"
          :class="[
            'px-4 py-2 rounded-lg font-medium transition-colors',
            sequence.active 
              ? 'bg-green-100 text-green-700 hover:bg-green-200' 
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          ]"
        >
          {{ sequence.active ? 'Active' : 'Inactive' }}
        </button>
        
        <button
          @click="saveSequence"
          class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
        >
          Save & Close
        </button>
      </div>
    </div>

    <!-- Main Content -->
    <div class="flex h-[calc(100vh-80px)]">
      <!-- Left Panel - Sequence Builder -->
      <div class="flex-1 flex flex-col">
        <!-- Sequence Info -->
        <div class="p-6 border-b border-gray-200 bg-gray-50">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Sequence Name</label>
              <input
                v-model="sequence.name"
                @input="triggerAutoSave"
                type="text"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                placeholder="Welcome Series"
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Trigger</label>
              <select
                v-model="sequence.trigger"
                @change="triggerAutoSave"
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
              >
                <option value="manual">Manual Start</option>
                <option value="tag-added">Tag Added</option>
                <option value="new-contact">New Contact</option>
                <option value="form-submit">Form Submit</option>
              </select>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Settings</label>
              <div class="flex items-center space-x-4">
                <label class="flex items-center">
                  <input
                    v-model="sequence.stopOnReply"
                    @change="triggerAutoSave"
                    type="checkbox"
                    class="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                  />
                  <span class="ml-2 text-sm text-gray-700">Stop on reply</span>
                </label>
              </div>
            </div>
          </div>
        </div>

        <!-- Sequence Steps -->
        <div class="flex-1 p-6 overflow-y-auto">
          <div class="flex items-center justify-between mb-6">
            <h2 class="text-lg font-medium text-gray-900">Email Sequence</h2>
            <button
              @click="addEmailStep"
              class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors flex items-center space-x-2"
            >
              <Plus class="w-4 h-4" />
              <span>Add Email</span>
            </button>
          </div>

          <!-- Draggable Email Steps -->
          <draggable
            v-model="sequence.steps"
            @change="handleStepReorder"
            item-key="id"
            class="space-y-4"
            ghost-class="opacity-50"
            chosen-class="shadow-lg"
          >
            <template #item="{ element: step, index }">
              <div class="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                <!-- Step Header -->
                <div class="flex items-center justify-between mb-4">
                  <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center cursor-move">
                      <span class="text-sm font-medium text-purple-600">{{ index + 1 }}</span>
                    </div>
                    <div>
                      <h3 class="font-medium text-gray-900">Email {{ index + 1 }}</h3>
                      <p class="text-sm text-gray-500">{{ step.delay.value }} {{ step.delay.unit }} delay</p>
                    </div>
                  </div>
                  
                  <div class="flex items-center space-x-2">
                    <!-- A/B Test Toggle -->
                    <button
                      @click="toggleABTest(step)"
                      :class="[
                        'px-3 py-1 text-xs rounded-full transition-colors',
                        step.abTest?.enabled 
                          ? 'bg-blue-100 text-blue-700' 
                          : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                      ]"
                    >
                      A/B Test
                    </button>
                    
                    <!-- Delete Step -->
                    <button
                      @click="removeStep(index)"
                      class="p-2 text-red-500 hover:bg-red-50 rounded-lg transition-colors"
                    >
                      <Trash2 class="w-4 h-4" />
                    </button>
                  </div>
                </div>

                <!-- Delay Settings -->
                <div class="grid grid-cols-3 gap-4 mb-4">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Delay</label>
                    <div class="flex space-x-2">
                      <input
                        v-model="step.delay.value"
                        @input="triggerAutoSave"
                        type="number"
                        min="0"
                        class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                      />
                      <select
                        v-model="step.delay.unit"
                        @change="triggerAutoSave"
                        class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                      >
                        <option value="minutes">Minutes</option>
                        <option value="hours">Hours</option>
                        <option value="days">Days</option>
                        <option value="weeks">Weeks</option>
                      </select>
                    </div>
                  </div>
                  
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Resend</label>
                    <div class="flex items-center space-x-2">
                      <input
                        v-model="step.resend.enabled"
                        @change="triggerAutoSave"
                        type="checkbox"
                        class="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                      />
                      <span class="text-sm text-gray-600">Enable resend</span>
                    </div>
                  </div>
                  
                  <div v-if="step.resend.enabled">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Resend After</label>
                    <div class="flex space-x-2">
                      <input
                        v-model="step.resend.delay"
                        @input="triggerAutoSave"
                        type="number"
                        min="1"
                        class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                      />
                      <select
                        v-model="step.resend.unit"
                        @change="triggerAutoSave"
                        class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                      >
                        <option value="hours">Hours</option>
                        <option value="days">Days</option>
                      </select>
                    </div>
                  </div>
                </div>

                <!-- A/B Test Section -->
                <div v-if="step.abTest?.enabled" class="mb-4 p-4 bg-blue-50 rounded-lg">
                  <h4 class="font-medium text-blue-900 mb-3">A/B Test Configuration</h4>
                  <div class="grid grid-cols-2 gap-4">
                    <div>
                      <label class="block text-sm font-medium text-blue-700 mb-1">Subject A (50%)</label>
                      <input
                        v-model="step.abTest.subjectA"
                        @input="triggerAutoSave"
                        type="text"
                        class="w-full px-3 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="First subject line"
                      />
                    </div>
                    <div>
                      <label class="block text-sm font-medium text-blue-700 mb-1">Subject B (50%)</label>
                      <input
                        v-model="step.abTest.subjectB"
                        @input="triggerAutoSave"
                        type="text"
                        class="w-full px-3 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Second subject line"
                      />
                    </div>
                  </div>
                  <div class="mt-3">
                    <label class="block text-sm font-medium text-blue-700 mb-1">Winner Selection</label>
                    <select
                      v-model="step.abTest.metric"
                      @change="triggerAutoSave"
                      class="w-full px-3 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="open-rate">Highest Open Rate</option>
                      <option value="click-rate">Highest Click Rate</option>
                      <option value="reply-rate">Highest Reply Rate</option>
                    </select>
                  </div>
                </div>

                <!-- Email Content -->
                <div class="space-y-4">
                  <div v-if="!step.abTest?.enabled">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Subject Line</label>
                    <input
                      v-model="step.subject"
                      @input="triggerAutoSave"
                      type="text"
                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                      placeholder="Email subject..."
                    />
                  </div>
                  
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Email Content</label>
                    <textarea
                      v-model="step.content"
                      @input="triggerAutoSave"
                      rows="6"
                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 font-mono text-sm"
                      placeholder="Write your email content here...

You can use variables like:
{{firstName}} - Contact's first name
{{company}} - Contact's company
{{customField}} - Any custom field

Basic HTML is supported:
<b>Bold text</b>
<i>Italic text</i>
<a href='https://example.com'>Link</a>"
                    ></textarea>
                    <p class="text-xs text-gray-500 mt-1">
                      Plain text + basic HTML supported. Variables: {{firstName}}, {{company}}, {{customField}}
                    </p>
                  </div>
                </div>

                <!-- Rules Section -->
                <div class="mt-4 pt-4 border-t border-gray-200">
                  <div class="flex items-center justify-between mb-3">
                    <h4 class="font-medium text-gray-900">Conditional Rules</h4>
                    <button
                      @click="addRule(step)"
                      class="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                    >
                      Add Rule
                    </button>
                  </div>
                  
                  <div v-if="step.rules && step.rules.length > 0" class="space-y-2">
                    <div
                      v-for="(rule, ruleIndex) in step.rules"
                      :key="ruleIndex"
                      class="flex items-center space-x-2 p-3 bg-gray-50 rounded-lg"
                    >
                      <span class="text-sm font-medium text-gray-600">IF</span>
                      <select
                        v-model="rule.condition"
                        @change="triggerAutoSave"
                        class="px-2 py-1 border border-gray-300 rounded text-sm"
                      >
                        <option value="opened">Email was opened</option>
                        <option value="not-opened">Email was not opened</option>
                        <option value="clicked">Link was clicked</option>
                        <option value="replied">Contact replied</option>
                        <option value="tag-has">Contact has tag</option>
                      </select>
                      
                      <span class="text-sm font-medium text-gray-600">THEN</span>
                      <select
                        v-model="rule.action"
                        @change="triggerAutoSave"
                        class="px-2 py-1 border border-gray-300 rounded text-sm"
                      >
                        <option value="skip-next">Skip next email</option>
                        <option value="add-tag">Add tag</option>
                        <option value="remove-tag">Remove tag</option>
                        <option value="end-sequence">End sequence</option>
                        <option value="send-different">Send different email</option>
                      </select>
                      
                      <button
                        @click="removeRule(step, ruleIndex)"
                        class="p-1 text-red-500 hover:bg-red-50 rounded"
                      >
                        <X class="w-3 h-3" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </draggable>
        </div>
      </div>

      <!-- Right Sidebar - Stats -->
      <div class="w-80 border-l border-gray-200 bg-gray-50 overflow-y-auto">
        <div class="p-6">
          <h2 class="text-lg font-medium text-gray-900 mb-6">Real-time Stats</h2>
          
          <!-- Overall Stats -->
          <div class="bg-white rounded-lg p-4 mb-6">
            <h3 class="font-medium text-gray-900 mb-4">Sequence Overview</h3>
            <div class="space-y-3">
              <div class="flex justify-between">
                <span class="text-sm text-gray-600">Total Enrolled</span>
                <span class="font-medium">{{ stats.totalEnrolled }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-sm text-gray-600">Active</span>
                <span class="font-medium text-green-600">{{ stats.active }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-sm text-gray-600">Completed</span>
                <span class="font-medium text-blue-600">{{ stats.completed }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-sm text-gray-600">Avg. Open Rate</span>
                <span class="font-medium">{{ stats.avgOpenRate }}%</span>
              </div>
            </div>
          </div>

          <!-- Per-Email Stats -->
          <div class="space-y-4">
            <h3 class="font-medium text-gray-900">Email Performance</h3>
            <div
              v-for="(step, index) in sequence.steps"
              :key="step.id"
              class="bg-white rounded-lg p-4"
            >
              <div class="flex items-center justify-between mb-3">
                <h4 class="font-medium text-gray-900">Email {{ index + 1 }}</h4>
                <span :class="[
                  'px-2 py-1 text-xs rounded-full',
                  step.abTest?.enabled ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-600'
                ]">
                  {{ step.abTest?.enabled ? 'A/B Test' : 'Standard' }}
                </span>
              </div>
              
              <div class="space-y-2">
                <div class="flex justify-between text-sm">
                  <span class="text-gray-600">Sent</span>
                  <span class="font-medium">{{ step.stats?.sent || 0 }}</span>
                </div>
                <div class="flex justify-between text-sm">
                  <span class="text-gray-600">Opened</span>
                  <span class="font-medium text-green-600">{{ step.stats?.opened || 0 }} ({{ step.stats?.openRate || 0 }}%)</span>
                </div>
                <div class="flex justify-between text-sm">
                  <span class="text-gray-600">Clicked</span>
                  <span class="font-medium text-blue-600">{{ step.stats?.clicked || 0 }} ({{ step.stats?.clickRate || 0 }}%)</span>
                </div>
                <div class="flex justify-between text-sm">
                  <span class="text-gray-600">Replied</span>
                  <span class="font-medium text-purple-600">{{ step.stats?.replied || 0 }} ({{ step.stats?.replyRate || 0 }}%)</span>
                </div>
              </div>

              <!-- A/B Test Results -->
              <div v-if="step.abTest?.enabled && step.abTest?.results" class="mt-3 pt-3 border-t border-gray-200">
                <p class="text-xs font-medium text-gray-700 mb-2">A/B Test Results</p>
                <div class="grid grid-cols-2 gap-2 text-xs">
                  <div class="text-center p-2 bg-gray-50 rounded">
                    <p class="font-medium">Subject A</p>
                    <p class="text-green-600">{{ step.abTest.results.aOpenRate }}% open</p>
                  </div>
                  <div class="text-center p-2 bg-gray-50 rounded">
                    <p class="font-medium">Subject B</p>
                    <p class="text-green-600">{{ step.abTest.results.bOpenRate }}% open</p>
                  </div>
                </div>
                <p class="text-xs text-center mt-2 font-medium text-blue-600">
                  Winner: Subject {{ step.abTest.results.winner }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue';
import { ArrowLeft, Plus, Trash2, X } from 'lucide-vue-next';
import draggable from 'vuedraggable';

interface SequenceStep {
  id: string;
  subject: string;
  content: string;
  delay: {
    value: number;
    unit: 'minutes' | 'hours' | 'days' | 'weeks';
  };
  resend: {
    enabled: boolean;
    delay: number;
    unit: 'hours' | 'days';
  };
  abTest?: {
    enabled: boolean;
    subjectA: string;
    subjectB: string;
    metric: 'open-rate' | 'click-rate' | 'reply-rate';
    results?: {
      aOpenRate: number;
      bOpenRate: number;
      winner: 'A' | 'B';
    };
  };
  rules?: Array<{
    condition: string;
    action: string;
    value?: string;
  }>;
  stats?: {
    sent: number;
    opened: number;
    clicked: number;
    replied: number;
    openRate: number;
    clickRate: number;
    replyRate: number;
  };
}

interface EmailSequence {
  id?: string;
  name: string;
  trigger: string;
  active: boolean;
  stopOnReply: boolean;
  steps: SequenceStep[];
}

interface Props {
  isOpen: boolean;
  sequence?: EmailSequence;
  columnTitle: string;
}

interface Emits {
  (e: 'close'): void;
  (e: 'save', sequence: EmailSequence): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// State
const sequence = ref<EmailSequence>({
  name: '',
  trigger: 'manual',
  active: false,
  stopOnReply: true,
  steps: []
});

const autoSaveStatus = ref<'idle' | 'saving' | 'saved'>('idle');
const autoSaveTimeout = ref<NodeJS.Timeout | null>(null);

// Mock stats data
const stats = ref({
  totalEnrolled: 156,
  active: 89,
  completed: 67,
  avgOpenRate: 45
});

// Computed
const autoSaveText = computed(() => {
  switch (autoSaveStatus.value) {
    case 'saving': return 'Saving...';
    case 'saved': return 'Saved';
    default: return 'Draft';
  }
});

// Methods
const initializeSequence = () => {
  if (props.sequence) {
    sequence.value = { ...props.sequence };
  } else {
    // Create new sequence with one default step
    sequence.value = {
      name: 'New Sequence',
      trigger: 'manual',
      active: false,
      stopOnReply: true,
      steps: [createDefaultStep()]
    };
  }
};

const createDefaultStep = (): SequenceStep => ({
  id: `step-${Date.now()}`,
  subject: '',
  content: '',
  delay: { value: 0, unit: 'days' },
  resend: { enabled: false, delay: 3, unit: 'days' },
  rules: [],
  stats: {
    sent: 0,
    opened: 0,
    clicked: 0,
    replied: 0,
    openRate: 0,
    clickRate: 0,
    replyRate: 0
  }
});

const addEmailStep = () => {
  const newStep = createDefaultStep();
  newStep.delay.value = 1; // Default 1 day delay for new steps
  sequence.value.steps.push(newStep);
  triggerAutoSave();
};

const removeStep = (index: number) => {
  if (sequence.value.steps.length > 1) {
    sequence.value.steps.splice(index, 1);
    triggerAutoSave();
  }
};

const handleStepReorder = () => {
  triggerAutoSave();
};

const toggleABTest = (step: SequenceStep) => {
  if (!step.abTest) {
    step.abTest = {
      enabled: true,
      subjectA: step.subject || '',
      subjectB: '',
      metric: 'open-rate'
    };
  } else {
    step.abTest.enabled = !step.abTest.enabled;
  }
  triggerAutoSave();
};

const addRule = (step: SequenceStep) => {
  if (!step.rules) {
    step.rules = [];
  }
  step.rules.push({
    condition: 'opened',
    action: 'skip-next'
  });
  triggerAutoSave();
};

const removeRule = (step: SequenceStep, ruleIndex: number) => {
  if (step.rules) {
    step.rules.splice(ruleIndex, 1);
    triggerAutoSave();
  }
};

const toggleSequenceStatus = () => {
  sequence.value.active = !sequence.value.active;
  triggerAutoSave();
};

const triggerAutoSave = () => {
  autoSaveStatus.value = 'saving';
  
  if (autoSaveTimeout.value) {
    clearTimeout(autoSaveTimeout.value);
  }
  
  autoSaveTimeout.value = setTimeout(() => {
    // Save to localStorage
    localStorage.setItem(`sequence-draft-${sequence.value.id || 'new'}`, JSON.stringify(sequence.value));
    autoSaveStatus.value = 'saved';
    
    setTimeout(() => {
      autoSaveStatus.value = 'idle';
    }, 2000);
  }, 1000);
};

const saveSequence = () => {
  emit('save', sequence.value);
  closeBuilder();
};

const closeBuilder = () => {
  if (autoSaveTimeout.value) {
    clearTimeout(autoSaveTimeout.value);
  }
  emit('close');
};

// Lifecycle
onMounted(() => {
  initializeSequence();
  
  // Load draft from localStorage if exists
  const draftKey = `sequence-draft-${sequence.value.id || 'new'}`;
  const draft = localStorage.getItem(draftKey);
  if (draft) {
    try {
      const parsedDraft = JSON.parse(draft);
      sequence.value = parsedDraft;
    } catch (error) {
      console.error('Error loading draft:', error);
    }
  }
});

onUnmounted(() => {
  if (autoSaveTimeout.value) {
    clearTimeout(autoSaveTimeout.value);
  }
});

// Watch for sequence changes
watch(() => props.sequence, () => {
  if (props.isOpen) {
    initializeSequence();
  }
}, { deep: true });
</script>
