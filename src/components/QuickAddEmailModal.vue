<template>
  <div
    v-if="isOpen"
    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
    @click.self="closeModal"
  >
    <div class="bg-white rounded-lg shadow-xl w-full max-w-2xl mx-4 max-h-[90vh] overflow-hidden">
      <!-- Header -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200">
        <div class="flex items-center space-x-3">
          <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
            <Plus class="w-4 h-4 text-blue-600" />
          </div>
          <div>
            <h2 class="text-lg font-semibold text-gray-900">Quick Add Email</h2>
            <p class="text-sm text-gray-500">Add to {{ columnTitle }}</p>
          </div>
        </div>
        <button
          @click="closeModal"
          class="p-2 hover:bg-gray-100 rounded-lg transition-colors"
        >
          <X class="w-5 h-5 text-gray-400" />
        </button>
      </div>

      <!-- Form -->
      <form @submit.prevent="handleSubmit" class="p-6 space-y-6">
        <!-- From Field -->
        <div>
          <label for="from" class="block text-sm font-medium text-gray-700 mb-2">
            From Email *
          </label>
          <input
            id="from"
            v-model="form.from"
            type="email"
            required
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
            placeholder="<EMAIL>"
          />
          <p v-if="errors.from" class="mt-1 text-sm text-red-600">{{ errors.from }}</p>
        </div>

        <!-- Subject Field -->
        <div>
          <label for="subject" class="block text-sm font-medium text-gray-700 mb-2">
            Subject *
          </label>
          <input
            id="subject"
            v-model="form.subject"
            type="text"
            required
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
            placeholder="Enter email subject"
          />
          <p v-if="errors.subject" class="mt-1 text-sm text-red-600">{{ errors.subject }}</p>
        </div>

        <!-- Priority Field -->
        <div>
          <label for="priority" class="block text-sm font-medium text-gray-700 mb-2">
            Priority
          </label>
          <select
            id="priority"
            v-model="form.priority"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
          >
            <option value="Low">Low</option>
            <option value="Medium">Medium</option>
            <option value="High">High</option>
          </select>
        </div>

        <!-- Content Field -->
        <div>
          <label for="content" class="block text-sm font-medium text-gray-700 mb-2">
            Email Content *
          </label>
          <textarea
            id="content"
            v-model="form.content"
            required
            rows="6"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors resize-none"
            placeholder="Enter email content..."
          ></textarea>
          <p v-if="errors.content" class="mt-1 text-sm text-red-600">{{ errors.content }}</p>
        </div>

        <!-- Tags Field -->
        <div>
          <label for="tags" class="block text-sm font-medium text-gray-700 mb-2">
            Tags (optional)
          </label>
          <input
            id="tags"
            v-model="tagsInput"
            type="text"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
            placeholder="Enter tags separated by commas"
          />
          <p class="mt-1 text-xs text-gray-500">Separate multiple tags with commas</p>
        </div>

        <!-- Action Buttons -->
        <div class="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
          <button
            type="button"
            @click="closeModal"
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            :disabled="isSubmitting"
            class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
          >
            <Loader2 v-if="isSubmitting" class="w-4 h-4 animate-spin" />
            <Plus v-else class="w-4 h-4" />
            <span>{{ isSubmitting ? 'Adding...' : 'Add Email' }}</span>
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { Plus, X, Loader2 } from 'lucide-vue-next';

interface EmailForm {
  from: string;
  subject: string;
  content: string;
  priority: 'Low' | 'Medium' | 'High';
}

interface Props {
  isOpen: boolean;
  columnId: string;
  columnTitle: string;
}

interface Emits {
  (e: 'close'): void;
  (e: 'create', email: EmailForm & { tags: string[]; columnId: string }): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// Form state
const form = ref<EmailForm>({
  from: '',
  subject: '',
  content: '',
  priority: 'Medium'
});

const tagsInput = ref('');
const isSubmitting = ref(false);
const errors = ref<Partial<Record<keyof EmailForm, string>>>({});

// Computed
const tags = computed(() => {
  return tagsInput.value
    .split(',')
    .map(tag => tag.trim())
    .filter(tag => tag.length > 0);
});

// Methods
const validateForm = (): boolean => {
  errors.value = {};
  
  if (!form.value.from.trim()) {
    errors.value.from = 'From email is required';
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.value.from)) {
    errors.value.from = 'Please enter a valid email address';
  }
  
  if (!form.value.subject.trim()) {
    errors.value.subject = 'Subject is required';
  }
  
  if (!form.value.content.trim()) {
    errors.value.content = 'Email content is required';
  }
  
  return Object.keys(errors.value).length === 0;
};

const handleSubmit = async () => {
  if (!validateForm()) return;
  
  isSubmitting.value = true;
  
  try {
    const emailData = {
      ...form.value,
      tags: tags.value,
      columnId: props.columnId
    };
    
    emit('create', emailData);
    resetForm();
  } catch (error) {
    console.error('Error creating email:', error);
  } finally {
    isSubmitting.value = false;
  }
};

const resetForm = () => {
  form.value = {
    from: '',
    subject: '',
    content: '',
    priority: 'Medium'
  };
  tagsInput.value = '';
  errors.value = {};
};

const closeModal = () => {
  resetForm();
  emit('close');
};

// Watch for modal open/close to reset form
watch(() => props.isOpen, (isOpen) => {
  if (!isOpen) {
    resetForm();
  }
});
</script>
