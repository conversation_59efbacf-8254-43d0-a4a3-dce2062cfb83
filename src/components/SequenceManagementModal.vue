<template>
  <div
    v-if="isOpen"
    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
    @click.self="closeModal"
  >
    <div class="bg-white rounded-lg shadow-xl w-full max-w-5xl mx-4 max-h-[90vh] overflow-hidden">
      <!-- Header -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200">
        <div class="flex items-center space-x-3">
          <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
            <Workflow class="w-4 h-4 text-purple-600" />
          </div>
          <div>
            <h2 class="text-lg font-semibold text-gray-900">{{ sequence?.name || 'Sequence Management' }}</h2>
            <p class="text-sm text-gray-500">{{ columnTitle }} - Manage automation sequence</p>
          </div>
        </div>
        <button
          @click="closeModal"
          class="p-2 hover:bg-gray-100 rounded-lg transition-colors"
        >
          <X class="w-5 h-5 text-gray-400" />
        </button>
      </div>

      <!-- Tab Navigation -->
      <div class="border-b border-gray-200">
        <nav class="flex space-x-8 px-6">
          <button
            v-for="tab in tabs"
            :key="tab.id"
            @click="activeTab = tab.id"
            :class="[
              'py-4 px-1 border-b-2 font-medium text-sm transition-colors',
              activeTab === tab.id
                ? 'border-purple-500 text-purple-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            ]"
          >
            <div class="flex items-center space-x-2">
              <component :is="tab.icon" class="w-4 h-4" />
              <span>{{ tab.name }}</span>
            </div>
          </button>
        </nav>
      </div>

      <!-- Content -->
      <div class="p-6 max-h-[calc(90vh-200px)] overflow-y-auto">
        <!-- Overview Tab -->
        <div v-if="activeTab === 'overview'">
          <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Sequence Info -->
            <div class="lg:col-span-2">
              <div class="bg-white border border-gray-200 rounded-lg p-6 mb-6">
                <div class="flex items-center justify-between mb-4">
                  <h3 class="text-lg font-medium text-gray-900">Sequence Details</h3>
                  <div class="flex items-center space-x-2">
                    <button
                      @click="toggleSequenceStatus"
                      :class="[
                        'w-12 h-6 rounded-full transition-colors relative',
                        sequence?.active ? 'bg-green-500' : 'bg-gray-300'
                      ]"
                    >
                      <div :class="[
                        'w-5 h-5 bg-white rounded-full transition-transform absolute top-0.5',
                        sequence?.active ? 'translate-x-6' : 'translate-x-0.5'
                      ]"></div>
                    </button>
                    <span class="text-sm text-gray-600">
                      {{ sequence?.active ? 'Active' : 'Inactive' }}
                    </span>
                  </div>
                </div>
                
                <div class="space-y-4">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Name</label>
                    <p class="text-gray-900">{{ sequence?.name }}</p>
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                    <p class="text-gray-600">{{ sequence?.description }}</p>
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Trigger</label>
                    <p class="text-gray-900">{{ sequence?.trigger }}</p>
                  </div>
                </div>
              </div>

              <!-- Sequence Steps -->
              <div class="bg-white border border-gray-200 rounded-lg p-6">
                <div class="flex items-center justify-between mb-4">
                  <h3 class="text-lg font-medium text-gray-900">Sequence Steps</h3>
                  <button
                    @click="editSequence"
                    class="px-3 py-2 text-sm bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                  >
                    Edit Sequence
                  </button>
                </div>
                
                <div class="space-y-4">
                  <div
                    v-for="(step, index) in sequence?.steps"
                    :key="index"
                    class="flex items-start space-x-4 p-4 border border-gray-200 rounded-lg"
                  >
                    <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0">
                      <span class="text-sm font-medium text-purple-600">{{ index + 1 }}</span>
                    </div>
                    <div class="flex-1">
                      <div class="flex items-center space-x-2 mb-2">
                        <h4 class="font-medium text-gray-900">{{ step.type === 'email' ? 'Send Email' : step.type }}</h4>
                        <span class="text-sm text-gray-500">
                          {{ step.delay.value }} {{ step.delay.unit }} delay
                        </span>
                      </div>
                      <p class="text-sm text-gray-600">{{ step.subject || step.description }}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Stats Sidebar -->
            <div class="space-y-6">
              <div class="bg-white border border-gray-200 rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Performance</h3>
                <div class="space-y-4">
                  <div class="text-center">
                    <p class="text-2xl font-bold text-gray-900">{{ sequence?.enrolledCount || 0 }}</p>
                    <p class="text-sm text-gray-500">Total Enrolled</p>
                  </div>
                  <div class="text-center">
                    <p class="text-2xl font-bold text-green-600">{{ sequence?.completedCount || 0 }}</p>
                    <p class="text-sm text-gray-500">Completed</p>
                  </div>
                  <div class="text-center">
                    <p class="text-2xl font-bold text-blue-600">{{ completionRate }}%</p>
                    <p class="text-sm text-gray-500">Completion Rate</p>
                  </div>
                </div>
              </div>

              <div class="bg-white border border-gray-200 rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
                <div class="space-y-3">
                  <button
                    @click="enrollContacts"
                    class="w-full px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                  >
                    Enroll Contacts
                  </button>
                  <button
                    @click="duplicateSequence"
                    class="w-full px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    Duplicate
                  </button>
                  <button
                    @click="exportSequenceData"
                    class="w-full px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    Export Data
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Enrolled Contacts Tab -->
        <div v-if="activeTab === 'contacts'">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-medium text-gray-900">Enrolled Contacts</h3>
            <div class="flex items-center space-x-3">
              <div class="relative">
                <Search class="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  v-model="contactSearch"
                  type="text"
                  placeholder="Search contacts..."
                  class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                />
              </div>
              <button
                @click="enrollContacts"
                class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
              >
                Enroll More
              </button>
            </div>
          </div>

          <!-- Contacts Table -->
          <div class="bg-white border border-gray-200 rounded-lg overflow-hidden">
            <div class="grid grid-cols-12 gap-4 p-4 bg-gray-50 border-b border-gray-200 text-sm font-medium text-gray-700">
              <div class="col-span-3">Contact</div>
              <div class="col-span-2">Current Step</div>
              <div class="col-span-2">Progress</div>
              <div class="col-span-2">Enrolled Date</div>
              <div class="col-span-2">Status</div>
              <div class="col-span-1">Actions</div>
            </div>
            
            <div
              v-for="contact in enrolledContacts"
              :key="contact.id"
              class="grid grid-cols-12 gap-4 p-4 border-b border-gray-100 hover:bg-gray-50 transition-colors"
            >
              <div class="col-span-3">
                <div class="flex items-center space-x-3">
                  <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                    <User class="w-4 h-4 text-purple-600" />
                  </div>
                  <div>
                    <p class="font-medium text-gray-900">{{ contact.name }}</p>
                    <p class="text-sm text-gray-500">{{ contact.email }}</p>
                  </div>
                </div>
              </div>
              <div class="col-span-2 text-sm text-gray-900">Step {{ contact.currentStep }}</div>
              <div class="col-span-2">
                <div class="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    class="bg-purple-600 h-2 rounded-full" 
                    :style="{ width: `${(contact.currentStep / (sequence?.steps.length || 1)) * 100}%` }"
                  ></div>
                </div>
                <p class="text-xs text-gray-500 mt-1">{{ contact.currentStep }}/{{ sequence?.steps.length }}</p>
              </div>
              <div class="col-span-2 text-sm text-gray-500">{{ formatDate(contact.enrolledDate) }}</div>
              <div class="col-span-2">
                <span :class="[
                  'px-2 py-1 text-xs rounded-full',
                  contact.status === 'active' ? 'bg-green-100 text-green-700' :
                  contact.status === 'paused' ? 'bg-yellow-100 text-yellow-700' :
                  'bg-gray-100 text-gray-700'
                ]">
                  {{ contact.status }}
                </span>
              </div>
              <div class="col-span-1">
                <button
                  @click="manageContact(contact)"
                  class="text-purple-600 hover:text-purple-700"
                >
                  <MoreVertical class="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Analytics Tab -->
        <div v-if="activeTab === 'analytics'">
          <h3 class="text-lg font-medium text-gray-900 mb-6">Sequence Analytics</h3>
          
          <!-- Performance Metrics -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white border border-gray-200 rounded-lg p-6">
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-sm font-medium text-gray-600">Open Rate</p>
                  <p class="text-2xl font-bold text-gray-900">{{ analytics.openRate }}%</p>
                </div>
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Mail class="w-6 h-6 text-blue-600" />
                </div>
              </div>
              <p class="text-sm text-green-600 mt-2">+5% from last month</p>
            </div>
            
            <div class="bg-white border border-gray-200 rounded-lg p-6">
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-sm font-medium text-gray-600">Click Rate</p>
                  <p class="text-2xl font-bold text-gray-900">{{ analytics.clickRate }}%</p>
                </div>
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <MousePointer class="w-6 h-6 text-green-600" />
                </div>
              </div>
              <p class="text-sm text-green-600 mt-2">+2% improvement</p>
            </div>
            
            <div class="bg-white border border-gray-200 rounded-lg p-6">
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-sm font-medium text-gray-600">Reply Rate</p>
                  <p class="text-2xl font-bold text-gray-900">{{ analytics.replyRate }}%</p>
                </div>
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                  <MessageSquare class="w-6 h-6 text-purple-600" />
                </div>
              </div>
              <p class="text-sm text-blue-600 mt-2">{{ analytics.totalReplies }} total replies</p>
            </div>
            
            <div class="bg-white border border-gray-200 rounded-lg p-6">
              <div class="flex items-center justify-between">
                <div>
                  <p class="text-sm font-medium text-gray-600">Unsubscribe Rate</p>
                  <p class="text-2xl font-bold text-gray-900">{{ analytics.unsubscribeRate }}%</p>
                </div>
                <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                  <UserMinus class="w-6 h-6 text-red-600" />
                </div>
              </div>
              <p class="text-sm text-red-600 mt-2">{{ analytics.totalUnsubscribes }} unsubscribed</p>
            </div>
          </div>
          
          <!-- Charts -->
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div class="bg-white border border-gray-200 rounded-lg p-6">
              <h4 class="font-medium text-gray-900 mb-4">Step Performance</h4>
              <div class="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
                <p class="text-gray-500">Chart: Performance by sequence step</p>
              </div>
            </div>
            
            <div class="bg-white border border-gray-200 rounded-lg p-6">
              <h4 class="font-medium text-gray-900 mb-4">Engagement Over Time</h4>
              <div class="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
                <p class="text-gray-500">Chart: Engagement metrics timeline</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import {
  Workflow, X, Search, User, MoreVertical, Mail, MousePointer,
  MessageSquare, UserMinus, BarChart3, Users
} from 'lucide-vue-next';

interface Props {
  isOpen: boolean;
  sequence: any;
  columnTitle: string;
}

interface Emits {
  (e: 'close'): void;
  (e: 'edit-sequence'): void;
  (e: 'enroll-contacts'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// State
const activeTab = ref('overview');
const contactSearch = ref('');

// Tabs
const tabs = ref([
  { id: 'overview', name: 'Overview', icon: BarChart3 },
  { id: 'contacts', name: 'Enrolled Contacts', icon: Users },
  { id: 'analytics', name: 'Analytics', icon: BarChart3 }
]);

// Mock data
const enrolledContacts = ref([
  {
    id: '1',
    name: 'John Doe',
    email: '<EMAIL>',
    currentStep: 2,
    enrolledDate: '2024-01-10T10:00:00Z',
    status: 'active'
  },
  {
    id: '2',
    name: 'Jane Smith',
    email: '<EMAIL>',
    currentStep: 1,
    enrolledDate: '2024-01-12T15:30:00Z',
    status: 'paused'
  }
]);

const analytics = ref({
  openRate: 45,
  clickRate: 12,
  replyRate: 8,
  unsubscribeRate: 2,
  totalReplies: 23,
  totalUnsubscribes: 5
});

// Computed
const completionRate = computed(() => {
  if (!props.sequence?.enrolledCount || !props.sequence?.completedCount) return 0;
  return Math.round((props.sequence.completedCount / props.sequence.enrolledCount) * 100);
});

// Methods
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString();
};

const toggleSequenceStatus = () => {
  if (props.sequence) {
    props.sequence.active = !props.sequence.active;
  }
};

const editSequence = () => {
  emit('edit-sequence');
};

const enrollContacts = () => {
  emit('enroll-contacts');
};

const duplicateSequence = () => {
  console.log('Duplicate sequence:', props.sequence?.id);
};

const exportSequenceData = () => {
  console.log('Export sequence data:', props.sequence?.id);
};

const manageContact = (contact: any) => {
  console.log('Manage contact in sequence:', contact);
};

const closeModal = () => {
  emit('close');
};
</script>
