import React from 'react';
import { 
  BarChart3, 
  Users, 
  Settings, 
  Mail, 
  Zap, 
  FileText, 
  Calendar,
  HelpCircle,
  Bookmark,
  Archive
} from 'lucide-react';

interface SidebarProps {
  activeView: string;
  onViewChange: (view: string) => void;
}

const Sidebar: React.FC<SidebarProps> = ({ activeView, onViewChange }) => {
  const menuItems = [
    { id: 'board', icon: Mail, label: 'Papan Email', count: 12 },
    { id: 'analytics', icon: BarChart3, label: 'Analytics' },
    { id: 'team', icon: Users, label: 'Tim', count: 3 },
    { id: 'templates', icon: FileText, label: 'Template' },
    { id: 'automation', icon: Zap, label: 'Otomati<PERSON><PERSON>' },
    { id: 'calendar', icon: Calendar, label: '<PERSON>lender' },
    { id: 'archive', icon: Archive, label: 'Arsip' },
    { id: 'bookmarks', icon: Bookmark, label: 'Bookmark' },
  ];

  const bottomItems = [
    { id: 'settings', icon: Settings, label: 'Pengaturan' },
    { id: 'help', icon: HelpCircle, label: 'Bantuan' },
  ];

  return (
    <div className="w-64 bg-white border-r border-gray-200 h-full flex flex-col">
      {/* Main Navigation */}
      <nav className="flex-1 px-4 py-6 space-y-1">
        {menuItems.map((item) => (
          <button
            key={item.id}
            onClick={() => onViewChange(item.id)}
            className={`w-full flex items-center justify-between px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
              activeView === item.id
                ? 'bg-blue-50 text-blue-700 border border-blue-200'
                : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
            }`}
          >
            <div className="flex items-center space-x-3">
              <item.icon className="w-5 h-5" />
              <span>{item.label}</span>
            </div>
            {item.count && (
              <span className={`text-xs px-2 py-1 rounded-full ${
                activeView === item.id
                  ? 'bg-blue-100 text-blue-700'
                  : 'bg-gray-200 text-gray-600'
              }`}>
                {item.count}
              </span>
            )}
          </button>
        ))}
      </nav>

      {/* Bottom Navigation */}
      <div className="px-4 py-4 space-y-1 border-t border-gray-200">
        {bottomItems.map((item) => (
          <button
            key={item.id}
            onClick={() => onViewChange(item.id)}
            className={`w-full flex items-center space-x-3 px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
              activeView === item.id
                ? 'bg-blue-50 text-blue-700'
                : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
            }`}
          >
            <item.icon className="w-5 h-5" />
            <span>{item.label}</span>
          </button>
        ))}
      </div>

      {/* License Info */}
      <div className="px-4 py-4 border-t border-gray-200">
        <div className="bg-gradient-to-r from-blue-50 to-teal-50 rounded-lg p-3">
          <div className="flex items-center space-x-2 mb-2">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span className="text-xs font-medium text-gray-700">Lisensi Aktif</span>
          </div>
          <p className="text-xs text-gray-600">Pro Plan</p>
          <p className="text-xs text-gray-500">Berlaku hingga 30 Des 2025</p>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;