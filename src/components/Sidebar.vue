<template>
  <div class="w-64 bg-white border-r border-gray-200 h-full flex flex-col">
    <!-- Main Navigation -->
    <nav class="flex-1 px-4 py-6 space-y-1">
      <router-link
        v-for="item in menuItems"
        :key="item.id"
        :to="item.route"
        class="w-full flex items-center justify-between px-3 py-2 text-sm font-medium rounded-lg transition-colors"
        :class="[
          $route.name === item.id
            ? 'bg-blue-50 text-blue-700 border border-blue-200'
            : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
        ]"
      >
        <div class="flex items-center space-x-3">
          <component :is="item.icon" class="w-5 h-5" />
          <span>{{ item.label }}</span>
        </div>
        <span
          v-if="item.count"
          class="text-xs px-2 py-1 rounded-full"
          :class="[
            $route.name === item.id
              ? 'bg-blue-100 text-blue-700'
              : 'bg-gray-200 text-gray-600'
          ]"
        >
          {{ item.count }}
        </span>
      </router-link>
    </nav>

    <!-- Bottom Navigation -->
    <div class="px-4 py-4 space-y-1 border-t border-gray-200">
      <button
        v-for="item in bottomItems"
        :key="item.id"
        @click="handleBottomItemClick(item.id)"
        class="w-full flex items-center space-x-3 px-3 py-2 text-sm font-medium rounded-lg transition-colors text-gray-700 hover:bg-gray-50 hover:text-gray-900"
      >
        <component :is="item.icon" class="w-5 h-5" />
        <span>{{ item.label }}</span>
      </button>
    </div>

    <!-- License Info -->
    <div class="px-4 py-4 border-t border-gray-200">
      <div class="bg-gradient-to-r from-blue-50 to-teal-50 rounded-lg p-3">
        <div class="flex items-center space-x-2 mb-2">
          <div class="w-2 h-2 bg-green-500 rounded-full"></div>
          <span class="text-xs font-medium text-gray-700">License Active</span>
        </div>
        <p class="text-xs text-gray-600">Pro Plan</p>
        <p class="text-xs text-gray-500">Valid until Dec 30, 2025</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { 
  BarChart3, 
  Users, 
  Settings, 
  Mail, 
  Zap, 
  FileText, 
  Calendar,
  HelpCircle,
  Bookmark,
  Archive
} from 'lucide-vue-next';

const menuItems = [
  { id: 'board', icon: Mail, label: 'Email Board', count: 12, route: '/' },
  { id: 'analytics', icon: BarChart3, label: 'Analytics', route: '/analytics' },
  { id: 'team', icon: Users, label: 'Team', count: 3, route: '/team' },
  { id: 'templates', icon: FileText, label: 'Templates', route: '/templates' },
  { id: 'automation', icon: Zap, label: 'Automation', route: '/automation' },
  { id: 'calendar', icon: Calendar, label: 'Calendar', route: '/calendar' },
  { id: 'archive', icon: Archive, label: 'Archive', route: '/archive' },
  { id: 'bookmarks', icon: Bookmark, label: 'Bookmarks', route: '/bookmarks' },
];

const bottomItems = [
  { id: 'settings', icon: Settings, label: 'Settings' },
  { id: 'help', icon: HelpCircle, label: 'Help' },
];

const handleBottomItemClick = (itemId: string) => {
  console.log(`Clicked ${itemId}`);
  // Handle settings and help clicks
};
</script>
