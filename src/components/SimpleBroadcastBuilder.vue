<template>
  <div
    v-if="isOpen"
    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
    @click.self="closeModal"
  >
    <div class="bg-white rounded-lg shadow-xl w-full max-w-3xl mx-4 max-h-[90vh] overflow-hidden">
      <!-- Header -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200">
        <div class="flex items-center space-x-3">
          <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
            <Megaphone class="w-4 h-4 text-orange-600" />
          </div>
          <div>
            <h2 class="text-lg font-semibold text-gray-900">Broadcast Builder</h2>
            <p class="text-sm text-gray-500">Send emails to multiple recipients</p>
          </div>
        </div>
        <button
          @click="closeModal"
          class="p-2 hover:bg-gray-100 rounded-lg transition-colors"
        >
          <X class="w-5 h-5 text-gray-400" />
        </button>
      </div>

      <!-- Content -->
      <div class="p-6 space-y-6 max-h-[calc(90vh-200px)] overflow-y-auto">
        <!-- Broadcast Info -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Broadcast Name *</label>
            <input
              v-model="broadcast.name"
              type="text"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              placeholder="Monthly Newsletter"
            />
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Send Time</label>
            <select
              v-model="broadcast.sendTime"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
            >
              <option value="now">Send Now</option>
              <option value="scheduled">Schedule for Later</option>
              <option value="draft">Save as Draft</option>
            </select>
          </div>
        </div>

        <!-- Schedule Date/Time -->
        <div v-if="broadcast.sendTime === 'scheduled'" class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Schedule Date</label>
            <input
              v-model="broadcast.scheduleDate"
              type="date"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
            />
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Schedule Time</label>
            <input
              v-model="broadcast.scheduleTime"
              type="time"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
            />
          </div>
        </div>

        <!-- Recipients -->
        <div>
          <h3 class="text-sm font-medium text-gray-700 mb-4">Recipients</h3>
          
          <!-- Recipient Selection Method -->
          <div class="mb-4">
            <div class="flex space-x-4">
              <label class="flex items-center">
                <input
                  v-model="broadcast.recipientMethod"
                  type="radio"
                  value="all"
                  class="text-orange-600 focus:ring-orange-500"
                />
                <span class="ml-2 text-sm text-gray-700">All Contacts</span>
              </label>
              
              <label class="flex items-center">
                <input
                  v-model="broadcast.recipientMethod"
                  type="radio"
                  value="tags"
                  class="text-orange-600 focus:ring-orange-500"
                />
                <span class="ml-2 text-sm text-gray-700">By Tags</span>
              </label>
              
              <label class="flex items-center">
                <input
                  v-model="broadcast.recipientMethod"
                  type="radio"
                  value="manual"
                  class="text-orange-600 focus:ring-orange-500"
                />
                <span class="ml-2 text-sm text-gray-700">Manual Selection</span>
              </label>
            </div>
          </div>

          <!-- Tag Selection -->
          <div v-if="broadcast.recipientMethod === 'tags'" class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">Select Tags</label>
            <div class="flex flex-wrap gap-2">
              <button
                v-for="tag in availableTags"
                :key="tag"
                @click="toggleTag(tag)"
                :class="[
                  'px-3 py-1 text-sm rounded-full border transition-colors',
                  broadcast.selectedTags.includes(tag)
                    ? 'bg-orange-100 border-orange-300 text-orange-700'
                    : 'bg-gray-100 border-gray-300 text-gray-700 hover:bg-gray-200'
                ]"
              >
                {{ tag }}
              </button>
            </div>
          </div>

          <!-- Manual Email Input -->
          <div v-if="broadcast.recipientMethod === 'manual'" class="mb-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">Email Addresses</label>
            <textarea
              v-model="broadcast.manualEmails"
              rows="4"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              placeholder="Enter email addresses separated by commas or new lines..."
            ></textarea>
          </div>

          <!-- Recipient Count -->
          <div class="text-sm text-gray-600">
            <span class="font-medium">{{ estimatedRecipients }}</span> recipients will receive this broadcast
          </div>
        </div>

        <!-- Email Content -->
        <div>
          <h3 class="text-sm font-medium text-gray-700 mb-4">Email Content</h3>
          
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">From Email</label>
              <input
                v-model="broadcast.fromEmail"
                type="email"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                placeholder="<EMAIL>"
              />
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Subject Line *</label>
              <input
                v-model="broadcast.subject"
                type="text"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                placeholder="Your monthly update is here!"
              />
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Email Content *</label>
              <textarea
                v-model="broadcast.content"
                rows="8"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                placeholder="Write your broadcast message here..."
              ></textarea>
              <p class="text-xs text-gray-500 mt-1">You can use variables like {{customerName}} and {{company}}</p>
            </div>
          </div>
        </div>

        <!-- Broadcast Settings -->
        <div class="border-t border-gray-200 pt-6">
          <h3 class="text-sm font-medium text-gray-700 mb-4">Settings</h3>
          <div class="space-y-3">
            <label class="flex items-center">
              <input
                v-model="broadcast.trackOpens"
                type="checkbox"
                class="rounded border-gray-300 text-orange-600 focus:ring-orange-500"
              />
              <span class="ml-2 text-sm text-gray-700">Track email opens</span>
            </label>
            
            <label class="flex items-center">
              <input
                v-model="broadcast.trackClicks"
                type="checkbox"
                class="rounded border-gray-300 text-orange-600 focus:ring-orange-500"
              />
              <span class="ml-2 text-sm text-gray-700">Track link clicks</span>
            </label>
            
            <label class="flex items-center">
              <input
                v-model="broadcast.includeUnsubscribe"
                type="checkbox"
                class="rounded border-gray-300 text-orange-600 focus:ring-orange-500"
              />
              <span class="ml-2 text-sm text-gray-700">Include unsubscribe link</span>
            </label>
            
            <label class="flex items-center">
              <input
                v-model="broadcast.sendTestEmail"
                type="checkbox"
                class="rounded border-gray-300 text-orange-600 focus:ring-orange-500"
              />
              <span class="ml-2 text-sm text-gray-700">Send test email to myself first</span>
            </label>
          </div>
        </div>

        <!-- Preview -->
        <div v-if="broadcast.subject && broadcast.content" class="border-t border-gray-200 pt-6">
          <h3 class="text-sm font-medium text-gray-700 mb-4">Preview</h3>
          <div class="border border-gray-200 rounded-lg p-4 bg-gray-50">
            <div class="mb-2">
              <span class="text-xs font-medium text-gray-500">SUBJECT:</span>
              <p class="text-sm font-medium text-gray-900">{{ broadcast.subject }}</p>
            </div>
            <div>
              <span class="text-xs font-medium text-gray-500">CONTENT:</span>
              <div class="text-sm text-gray-900 whitespace-pre-wrap mt-1">{{ broadcast.content.substring(0, 200) }}{{ broadcast.content.length > 200 ? '...' : '' }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Footer -->
      <div class="flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50">
        <div class="text-sm text-gray-600">
          <span v-if="broadcast.sendTime === 'scheduled'">
            Scheduled for {{ broadcast.scheduleDate }} at {{ broadcast.scheduleTime }}
          </span>
        </div>
        
        <div class="flex items-center space-x-3">
          <button
            @click="closeModal"
            class="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          <button
            @click="saveBroadcast"
            :disabled="!broadcast.name || !broadcast.subject || !broadcast.content"
            class="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
          >
            <Megaphone class="w-4 h-4" />
            <span>{{ broadcast.sendTime === 'now' ? 'Send Broadcast' : broadcast.sendTime === 'scheduled' ? 'Schedule Broadcast' : 'Save Draft' }}</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { Megaphone, X } from 'lucide-vue-next';

interface Broadcast {
  name: string;
  sendTime: 'now' | 'scheduled' | 'draft';
  scheduleDate: string;
  scheduleTime: string;
  recipientMethod: 'all' | 'tags' | 'manual';
  selectedTags: string[];
  manualEmails: string;
  fromEmail: string;
  subject: string;
  content: string;
  trackOpens: boolean;
  trackClicks: boolean;
  includeUnsubscribe: boolean;
  sendTestEmail: boolean;
}

interface Props {
  isOpen: boolean;
}

interface Emits {
  (e: 'close'): void;
  (e: 'create', broadcast: Broadcast): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// State
const broadcast = ref<Broadcast>({
  name: '',
  sendTime: 'now',
  scheduleDate: '',
  scheduleTime: '',
  recipientMethod: 'all',
  selectedTags: [],
  manualEmails: '',
  fromEmail: '',
  subject: '',
  content: '',
  trackOpens: true,
  trackClicks: true,
  includeUnsubscribe: true,
  sendTestEmail: false
});

// Mock data
const availableTags = ref([
  'customers', 'prospects', 'newsletter', 'vip', 'new-users', 'inactive', 'support'
]);

// Computed
const estimatedRecipients = computed(() => {
  if (broadcast.value.recipientMethod === 'all') {
    return 1250; // Mock total
  } else if (broadcast.value.recipientMethod === 'tags') {
    return broadcast.value.selectedTags.length * 150; // Mock calculation
  } else if (broadcast.value.recipientMethod === 'manual') {
    const emails = broadcast.value.manualEmails.split(/[,\n]/).filter(email => email.trim());
    return emails.length;
  }
  return 0;
});

// Methods
const toggleTag = (tag: string) => {
  const index = broadcast.value.selectedTags.indexOf(tag);
  if (index > -1) {
    broadcast.value.selectedTags.splice(index, 1);
  } else {
    broadcast.value.selectedTags.push(tag);
  }
};

const saveBroadcast = () => {
  console.log('Creating broadcast:', broadcast.value);
  emit('create', broadcast.value);
  resetForm();
};

const resetForm = () => {
  broadcast.value = {
    name: '',
    sendTime: 'now',
    scheduleDate: '',
    scheduleTime: '',
    recipientMethod: 'all',
    selectedTags: [],
    manualEmails: '',
    fromEmail: '',
    subject: '',
    content: '',
    trackOpens: true,
    trackClicks: true,
    includeUnsubscribe: true,
    sendTestEmail: false
  };
};

const closeModal = () => {
  resetForm();
  emit('close');
};

// Watch for modal open/close
watch(() => props.isOpen, (isOpen) => {
  if (!isOpen) {
    resetForm();
  }
});
</script>
