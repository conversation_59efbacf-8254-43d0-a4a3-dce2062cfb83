<template>
  <div
    v-if="isOpen"
    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
    @click.self="closeModal"
  >
    <div class="bg-white rounded-lg shadow-xl w-full max-w-4xl mx-4 max-h-[90vh] overflow-hidden">
      <!-- Header -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200">
        <div class="flex items-center space-x-3">
          <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
            <Workflow class="w-4 h-4 text-purple-600" />
          </div>
          <div>
            <h2 class="text-lg font-semibold text-gray-900">Email Sequence Builder</h2>
            <p class="text-sm text-gray-500">Create automated email sequences</p>
          </div>
        </div>
        <button
          @click="closeModal"
          class="p-2 hover:bg-gray-100 rounded-lg transition-colors"
        >
          <X class="w-5 h-5 text-gray-400" />
        </button>
      </div>

      <!-- Content -->
      <div class="p-6 space-y-6 max-h-[calc(90vh-200px)] overflow-y-auto">
        <!-- Sequence Info -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Sequence Name *</label>
            <input
              v-model="sequence.name"
              type="text"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
              placeholder="Welcome Series"
            />
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Trigger</label>
            <select
              v-model="sequence.trigger"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
            >
              <option value="manual">Manual Start</option>
              <option value="new-email">New Email Received</option>
              <option value="tag-added">Tag Added</option>
              <option value="priority-high">High Priority Email</option>
              <option value="no-reply">No Reply After X Days</option>
            </select>
          </div>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
          <textarea
            v-model="sequence.description"
            rows="2"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
            placeholder="Describe what this sequence does..."
          ></textarea>
        </div>

        <!-- Sequence Steps -->
        <div>
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-sm font-medium text-gray-700">Sequence Steps</h3>
            <button
              @click="addStep"
              class="inline-flex items-center space-x-2 px-3 py-2 text-sm bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
            >
              <Plus class="w-4 h-4" />
              <span>Add Step</span>
            </button>
          </div>

          <div class="space-y-4">
            <div
              v-for="(step, index) in sequence.steps"
              :key="index"
              class="border border-gray-200 rounded-lg p-4"
            >
              <div class="flex items-center justify-between mb-4">
                <div class="flex items-center space-x-3">
                  <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                    <span class="text-sm font-medium text-purple-600">{{ index + 1 }}</span>
                  </div>
                  <h4 class="font-medium text-gray-900">Step {{ index + 1 }}</h4>
                </div>
                <button
                  v-if="sequence.steps.length > 1"
                  @click="removeStep(index)"
                  class="text-red-500 hover:text-red-700"
                >
                  <Trash2 class="w-4 h-4" />
                </button>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Action Type</label>
                  <select
                    v-model="step.type"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                  >
                    <option value="email">Send Email</option>
                    <option value="wait">Wait</option>
                    <option value="condition">Condition Check</option>
                    <option value="tag">Add Tag</option>
                    <option value="assign">Assign to Team</option>
                  </select>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Delay</label>
                  <div class="flex space-x-2">
                    <input
                      v-model="step.delay.value"
                      type="number"
                      min="0"
                      class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                    />
                    <select
                      v-model="step.delay.unit"
                      class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                    >
                      <option value="minutes">Minutes</option>
                      <option value="hours">Hours</option>
                      <option value="days">Days</option>
                      <option value="weeks">Weeks</option>
                    </select>
                  </div>
                </div>
              </div>

              <!-- Email Step -->
              <div v-if="step.type === 'email'" class="space-y-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Email Template</label>
                  <select
                    v-model="step.emailTemplate"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                  >
                    <option value="">Select template...</option>
                    <option value="welcome">Welcome Email</option>
                    <option value="follow-up">Follow-up Email</option>
                    <option value="reminder">Reminder Email</option>
                    <option value="thank-you">Thank You Email</option>
                  </select>
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Subject</label>
                  <input
                    v-model="step.subject"
                    type="text"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                    placeholder="Email subject..."
                  />
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Content</label>
                  <textarea
                    v-model="step.content"
                    rows="4"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                    placeholder="Email content..."
                  ></textarea>
                </div>
              </div>

              <!-- Wait Step -->
              <div v-if="step.type === 'wait'" class="space-y-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Wait Condition</label>
                  <select
                    v-model="step.waitCondition"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                  >
                    <option value="time">Wait for time</option>
                    <option value="reply">Wait for reply</option>
                    <option value="click">Wait for link click</option>
                    <option value="tag">Wait for tag</option>
                  </select>
                </div>
              </div>

              <!-- Tag Step -->
              <div v-if="step.type === 'tag'" class="space-y-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Tag to Add</label>
                  <input
                    v-model="step.tagName"
                    type="text"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                    placeholder="sequence-completed"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Sequence Settings -->
        <div class="border-t border-gray-200 pt-6">
          <h3 class="text-sm font-medium text-gray-700 mb-4">Settings</h3>
          <div class="space-y-3">
            <label class="flex items-center">
              <input
                v-model="sequence.active"
                type="checkbox"
                class="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
              />
              <span class="ml-2 text-sm text-gray-700">Activate sequence immediately</span>
            </label>
            
            <label class="flex items-center">
              <input
                v-model="sequence.stopOnReply"
                type="checkbox"
                class="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
              />
              <span class="ml-2 text-sm text-gray-700">Stop sequence when customer replies</span>
            </label>
            
            <label class="flex items-center">
              <input
                v-model="sequence.trackOpens"
                type="checkbox"
                class="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
              />
              <span class="ml-2 text-sm text-gray-700">Track email opens</span>
            </label>
          </div>
        </div>
      </div>

      <!-- Footer -->
      <div class="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50">
        <button
          @click="closeModal"
          class="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
        >
          Cancel
        </button>
        <button
          @click="saveSequence"
          :disabled="!sequence.name || sequence.steps.length === 0"
          class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
        >
          <Workflow class="w-4 h-4" />
          <span>Create Sequence</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { Workflow, X, Plus, Trash2 } from 'lucide-vue-next';

interface SequenceStep {
  type: 'email' | 'wait' | 'condition' | 'tag' | 'assign';
  delay: {
    value: number;
    unit: 'minutes' | 'hours' | 'days' | 'weeks';
  };
  emailTemplate?: string;
  subject?: string;
  content?: string;
  waitCondition?: string;
  tagName?: string;
}

interface EmailSequence {
  name: string;
  description: string;
  trigger: string;
  steps: SequenceStep[];
  active: boolean;
  stopOnReply: boolean;
  trackOpens: boolean;
}

interface Props {
  isOpen: boolean;
}

interface Emits {
  (e: 'close'): void;
  (e: 'create', sequence: EmailSequence): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// State
const sequence = ref<EmailSequence>({
  name: '',
  description: '',
  trigger: 'manual',
  steps: [
    {
      type: 'email',
      delay: { value: 0, unit: 'minutes' },
      subject: '',
      content: ''
    }
  ],
  active: true,
  stopOnReply: true,
  trackOpens: true
});

// Methods
const addStep = () => {
  sequence.value.steps.push({
    type: 'email',
    delay: { value: 1, unit: 'days' },
    subject: '',
    content: ''
  });
};

const removeStep = (index: number) => {
  sequence.value.steps.splice(index, 1);
};

const saveSequence = () => {
  console.log('Creating sequence:', sequence.value);
  emit('create', sequence.value);
  resetForm();
};

const resetForm = () => {
  sequence.value = {
    name: '',
    description: '',
    trigger: 'manual',
    steps: [
      {
        type: 'email',
        delay: { value: 0, unit: 'minutes' },
        subject: '',
        content: ''
      }
    ],
    active: true,
    stopOnReply: true,
    trackOpens: true
  };
};

const closeModal = () => {
  resetForm();
  emit('close');
};

// Watch for modal open/close
watch(() => props.isOpen, (isOpen) => {
  if (!isOpen) {
    resetForm();
  }
});
</script>
