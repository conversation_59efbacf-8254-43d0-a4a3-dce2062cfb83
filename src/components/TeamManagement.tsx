import React from 'react';
import { User, Mail, Shield, Settings, Plus, MessageCircle } from 'lucide-react';
import { Team } from '../types';

interface TeamManagementProps {
  team: Team[];
}

const TeamManagement: React.FC<TeamManagementProps> = ({ team }) => {
  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin':
        return 'bg-red-100 text-red-800';
      case 'manager':
        return 'bg-blue-100 text-blue-800';
      case 'agent':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online':
        return 'bg-green-500';
      case 'away':
        return 'bg-yellow-500';
      case 'offline':
        return 'bg-gray-400';
      default:
        return 'bg-gray-400';
    }
  };

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2"><PERSON><PERSON><PERSON><PERSON> Tim</h1>
          <p className="text-gray-600"><PERSON><PERSON><PERSON> anggota tim dan hak akses mereka</p>
        </div>
        <button className="bg-gradient-to-r from-blue-600 to-teal-600 text-white px-4 py-2 rounded-lg hover:from-blue-700 hover:to-teal-700 transition-all duration-200 flex items-center space-x-2">
          <Plus className="w-4 h-4" />
          <span>Undang Anggota</span>
        </button>
      </div>

      {/* Team Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center space-x-3">
            <User className="w-8 h-8 text-blue-600" />
            <div>
              <p className="text-2xl font-bold text-gray-900">{team.length}</p>
              <p className="text-sm text-gray-600">Total Anggota</p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center space-x-3">
            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
            <div>
              <p className="text-2xl font-bold text-gray-900">
                {team.filter(m => m.status === 'online').length}
              </p>
              <p className="text-sm text-gray-600">Online</p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center space-x-3">
            <Mail className="w-8 h-8 text-green-600" />
            <div>
              <p className="text-2xl font-bold text-gray-900">2.4</p>
              <p className="text-sm text-gray-600">Rata-rata Respon (jam)</p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center space-x-3">
            <MessageCircle className="w-8 h-8 text-purple-600" />
            <div>
              <p className="text-2xl font-bold text-gray-900">156</p>
              <p className="text-sm text-gray-600">Email Ditangani</p>
            </div>
          </div>
        </div>
      </div>

      {/* Team Members */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Anggota Tim</h2>
        </div>
        <div className="divide-y divide-gray-200">
          {team.map((member) => (
            <div key={member.id} className="px-6 py-4 hover:bg-gray-50 transition-colors">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="relative">
                    <img
                      src={member.avatar}
                      alt={member.name}
                      className="w-12 h-12 rounded-full"
                    />
                    <div className={`absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white ${getStatusColor(member.status)}`}></div>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">{member.name}</h3>
                    <p className="text-sm text-gray-600">{member.email}</p>
                    <div className="flex items-center space-x-2 mt-1">
                      <span className={`text-xs px-2 py-1 rounded-full ${getRoleColor(member.role)}`}>
                        {member.role === 'admin' ? 'Admin' : member.role === 'manager' ? 'Manager' : 'Agent'}
                      </span>
                      <span className="text-xs text-gray-500 capitalize">{member.status}</span>
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors">
                    <MessageCircle className="w-4 h-4" />
                  </button>
                  <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors">
                    <Settings className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Role Permissions */}
      <div className="mt-8 bg-white rounded-lg border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Hak Akses & Permission</h2>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-4">
              <h3 className="font-medium text-gray-900 flex items-center space-x-2">
                <Shield className="w-4 h-4 text-red-600" />
                <span>Admin</span>
              </h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Akses penuh ke semua fitur</li>
                <li>• Kelola tim dan permission</li>
                <li>• Konfigurasi AI dan otomatisasi</li>
                <li>• Analytics dan laporan</li>
                <li>• Pengaturan billing</li>
              </ul>
            </div>
            <div className="space-y-4">
              <h3 className="font-medium text-gray-900 flex items-center space-x-2">
                <Shield className="w-4 h-4 text-blue-600" />
                <span>Manager</span>
              </h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Kelola email dan workflow</li>
                <li>• Assign tugas ke agent</li>
                <li>• Lihat analytics tim</li>
                <li>• Konfigurasi template</li>
                <li>• Moderate conversation</li>
              </ul>
            </div>
            <div className="space-y-4">
              <h3 className="font-medium text-gray-900 flex items-center space-x-2">
                <Shield className="w-4 h-4 text-green-600" />
                <span>Agent</span>
              </h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Handle email yang di-assign</li>
                <li>• Update status dan priority</li>
                <li>• Gunakan template</li>
                <li>• Lihat analytics pribadi</li>
                <li>• Kolaborasi dengan tim</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TeamManagement;