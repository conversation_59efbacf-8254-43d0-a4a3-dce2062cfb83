<template>
  <div class="p-6">
    <div class="flex items-center justify-between mb-6">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 mb-2">Team Management</h1>
        <p class="text-gray-600">Manage team members and their access rights</p>
      </div>
      <button class="bg-gradient-to-r from-blue-600 to-teal-600 text-white px-4 py-2 rounded-lg hover:from-blue-700 hover:to-teal-700 transition-all duration-200 flex items-center space-x-2">
        <Plus class="w-4 h-4" />
        <span>Invite Member</span>
      </button>
    </div>

    <!-- Team Stats -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
      <div class="bg-white rounded-lg border border-gray-200 p-6">
        <div class="flex items-center space-x-3">
          <User class="w-8 h-8 text-blue-600" />
          <div>
            <p class="text-2xl font-bold text-gray-900">{{ team.length }}</p>
            <p class="text-sm text-gray-600">Total Members</p>
          </div>
        </div>
      </div>
      <div class="bg-white rounded-lg border border-gray-200 p-6">
        <div class="flex items-center space-x-3">
          <div class="w-3 h-3 bg-green-500 rounded-full"></div>
          <div>
            <p class="text-2xl font-bold text-gray-900">
              {{ team.filter(m => m.status === 'online').length }}
            </p>
            <p class="text-sm text-gray-600">Online</p>
          </div>
        </div>
      </div>
      <div class="bg-white rounded-lg border border-gray-200 p-6">
        <div class="flex items-center space-x-3">
          <Mail class="w-8 h-8 text-green-600" />
          <div>
            <p class="text-2xl font-bold text-gray-900">2.4</p>
            <p class="text-sm text-gray-600">Average Response (hours)</p>
          </div>
        </div>
      </div>
      <div class="bg-white rounded-lg border border-gray-200 p-6">
        <div class="flex items-center space-x-3">
          <MessageCircle class="w-8 h-8 text-purple-600" />
          <div>
            <p class="text-2xl font-bold text-gray-900">156</p>
            <p class="text-sm text-gray-600">Emails Handled</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Team Members -->
    <div class="bg-white rounded-lg border border-gray-200">
      <div class="px-6 py-4 border-b border-gray-200">
        <h2 class="text-lg font-semibold text-gray-900">Team Members</h2>
      </div>
      <div class="divide-y divide-gray-200">
        <div
          v-for="member in team"
          :key="member.id"
          class="px-6 py-4 hover:bg-gray-50 transition-colors"
        >
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
              <div class="relative">
                <img
                  :src="member.avatar"
                  :alt="member.name"
                  class="w-12 h-12 rounded-full"
                />
                <div :class="`absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white ${getStatusColor(member.status)}`"></div>
              </div>
              <div>
                <h3 class="font-semibold text-gray-900">{{ member.name }}</h3>
                <p class="text-sm text-gray-600">{{ member.email }}</p>
                <div class="flex items-center space-x-2 mt-1">
                  <span :class="`text-xs px-2 py-1 rounded-full ${getRoleColor(member.role)}`">
                    {{ getRoleText(member.role) }}
                  </span>
                  <span class="text-xs text-gray-500 capitalize">{{ member.status }}</span>
                </div>
              </div>
            </div>
            <div class="flex items-center space-x-2">
              <button class="p-2 text-gray-400 hover:text-gray-600 transition-colors">
                <MessageCircle class="w-4 h-4" />
              </button>
              <button class="p-2 text-gray-400 hover:text-gray-600 transition-colors">
                <Settings class="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Role Permissions -->
    <div class="mt-8 bg-white rounded-lg border border-gray-200">
      <div class="px-6 py-4 border-b border-gray-200">
        <h2 class="text-lg font-semibold text-gray-900">Access Rights & Permissions</h2>
      </div>
      <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div
            v-for="role in rolePermissions"
            :key="role.name"
            class="space-y-4"
          >
            <h3 class="font-medium text-gray-900 flex items-center space-x-2">
              <Shield :class="`w-4 h-4 ${role.color}`" />
              <span>{{ role.name }}</span>
            </h3>
            <ul class="text-sm text-gray-600 space-y-1">
              <li v-for="permission in role.permissions" :key="permission">
                • {{ permission }}
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { User, Mail, Shield, Settings, Plus, MessageCircle } from 'lucide-vue-next';
import type { Team } from '@/types';

defineProps<{
  team: Team[];
}>();

const getRoleColor = (role: string) => {
  switch (role) {
    case 'admin':
      return 'bg-red-100 text-red-800';
    case 'manager':
      return 'bg-blue-100 text-blue-800';
    case 'agent':
      return 'bg-green-100 text-green-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

const getStatusColor = (status: string) => {
  switch (status) {
    case 'online':
      return 'bg-green-500';
    case 'away':
      return 'bg-yellow-500';
    case 'offline':
      return 'bg-gray-400';
    default:
      return 'bg-gray-400';
  }
};

const getRoleText = (role: string) => {
  switch (role) {
    case 'admin': return 'Admin';
    case 'manager': return 'Manager';
    case 'agent': return 'Agent';
    default: return role;
  }
};

const rolePermissions = [
  {
    name: 'Admin',
    color: 'text-red-600',
    permissions: [
      'Full access to all features',
      'Manage team and permissions',
      'Configure AI and automation',
      'Analytics and reports',
      'Billing settings'
    ]
  },
  {
    name: 'Manager',
    color: 'text-blue-600',
    permissions: [
      'Manage emails and workflow',
      'Assign tasks to agents',
      'View team analytics',
      'Configure templates',
      'Moderate conversations'
    ]
  },
  {
    name: 'Agent',
    color: 'text-green-600',
    permissions: [
      'Handle assigned emails',
      'Update status and priority',
      'Use templates',
      'View personal analytics',
      'Collaborate with team'
    ]
  }
];
</script>
