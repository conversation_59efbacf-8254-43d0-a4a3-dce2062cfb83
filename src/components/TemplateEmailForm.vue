<template>
  <div
    v-if="isOpen"
    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
    @click.self="closeModal"
  >
    <div class="bg-white rounded-lg shadow-xl w-full max-w-3xl mx-4 max-h-[90vh] overflow-hidden">
      <!-- Header -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200">
        <div class="flex items-center space-x-3">
          <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
            <FileText class="w-4 h-4 text-purple-600" />
          </div>
          <div>
            <h2 class="text-lg font-semibold text-gray-900">Create from Template</h2>
            <p class="text-sm text-gray-500">{{ template?.name }} → {{ columnTitle }}</p>
          </div>
        </div>
        <button
          @click="closeModal"
          class="p-2 hover:bg-gray-100 rounded-lg transition-colors"
        >
          <X class="w-5 h-5 text-gray-400" />
        </button>
      </div>

      <!-- Form -->
      <form @submit.prevent="handleSubmit" class="flex-1 overflow-hidden">
        <div class="p-6 space-y-6 max-h-[calc(90vh-200px)] overflow-y-auto">
          <!-- Template Info -->
          <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
            <div class="flex items-center space-x-2 mb-2">
              <FileText class="w-4 h-4 text-purple-600" />
              <span class="text-sm font-medium text-purple-800">Template: {{ template?.name }}</span>
            </div>
            <p class="text-sm text-purple-700">{{ template?.description }}</p>
          </div>

          <!-- Variables Section -->
          <div v-if="templateVariables.length > 0">
            <h3 class="text-sm font-medium text-gray-700 mb-3">Template Variables</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div v-for="variable in templateVariables" :key="variable" class="space-y-2">
                <label :for="variable" class="block text-sm font-medium text-gray-700">
                  {{ formatVariableName(variable) }}
                </label>
                <input
                  :id="variable"
                  v-model="variables[variable]"
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                  :placeholder="`Enter ${formatVariableName(variable).toLowerCase()}`"
                />
              </div>
            </div>
          </div>

          <!-- From Field -->
          <div>
            <label for="from" class="block text-sm font-medium text-gray-700 mb-2">
              From Email *
            </label>
            <input
              id="from"
              v-model="form.from"
              type="email"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
              placeholder="<EMAIL>"
            />
          </div>

          <!-- Subject Field -->
          <div>
            <label for="subject" class="block text-sm font-medium text-gray-700 mb-2">
              Subject *
            </label>
            <input
              id="subject"
              v-model="form.subject"
              type="text"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
            />
            <p class="text-xs text-gray-500 mt-1">Variables will be replaced automatically</p>
          </div>

          <!-- Priority Field -->
          <div>
            <label for="priority" class="block text-sm font-medium text-gray-700 mb-2">
              Priority
            </label>
            <select
              id="priority"
              v-model="form.priority"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
            >
              <option value="Low">Low</option>
              <option value="Medium">Medium</option>
              <option value="High">High</option>
            </select>
          </div>

          <!-- Content Field -->
          <div>
            <label for="content" class="block text-sm font-medium text-gray-700 mb-2">
              Email Content *
            </label>
            <textarea
              id="content"
              v-model="form.content"
              required
              rows="8"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 resize-none"
            ></textarea>
            <p class="text-xs text-gray-500 mt-1">Variables like {{customerName}} will be replaced</p>
          </div>

          <!-- Tags Field -->
          <div>
            <label for="tags" class="block text-sm font-medium text-gray-700 mb-2">
              Tags (optional)
            </label>
            <input
              id="tags"
              v-model="tagsInput"
              type="text"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
              placeholder="template, customer-service"
            />
          </div>

          <!-- Preview Section -->
          <div class="border-t border-gray-200 pt-6">
            <h3 class="text-sm font-medium text-gray-700 mb-3">Preview</h3>
            <div class="bg-gray-50 border border-gray-200 rounded-lg p-4 space-y-3">
              <div>
                <span class="text-xs font-medium text-gray-500">SUBJECT:</span>
                <p class="text-sm text-gray-900">{{ processedSubject }}</p>
              </div>
              <div>
                <span class="text-xs font-medium text-gray-500">CONTENT:</span>
                <div class="text-sm text-gray-900 whitespace-pre-wrap max-h-32 overflow-y-auto">{{ processedContent }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Footer -->
        <div class="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50">
          <button
            type="button"
            @click="closeModal"
            class="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            :disabled="isSubmitting"
            class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
          >
            <Loader2 v-if="isSubmitting" class="w-4 h-4 animate-spin" />
            <FileText v-else class="w-4 h-4" />
            <span>{{ isSubmitting ? 'Creating...' : 'Create Email' }}</span>
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { FileText, X, Loader2 } from 'lucide-vue-next';

interface EmailTemplate {
  id: string;
  name: string;
  description: string;
  subject: string;
  content: string;
  category: string;
  variables?: string[];
}

interface Props {
  isOpen: boolean;
  template: EmailTemplate | null;
  columnId: string;
  columnTitle: string;
}

interface Emits {
  (e: 'close'): void;
  (e: 'create', email: any): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// Form state
const form = ref({
  from: '',
  subject: '',
  content: '',
  priority: 'Medium' as 'Low' | 'Medium' | 'High'
});

const variables = ref<Record<string, string>>({});
const tagsInput = ref('');
const isSubmitting = ref(false);

// Computed
const templateVariables = computed(() => {
  if (!props.template?.content) return [];
  
  const matches = props.template.content.match(/\{\{(\w+)\}\}/g);
  if (!matches) return [];
  
  return [...new Set(matches.map(match => match.replace(/[{}]/g, '')))];
});

const processedSubject = computed(() => {
  let subject = form.value.subject;
  Object.entries(variables.value).forEach(([key, value]) => {
    subject = subject.replace(new RegExp(`\\{\\{${key}\\}\\}`, 'g'), value || `{{${key}}}`);
  });
  return subject;
});

const processedContent = computed(() => {
  let content = form.value.content;
  Object.entries(variables.value).forEach(([key, value]) => {
    content = content.replace(new RegExp(`\\{\\{${key}\\}\\}`, 'g'), value || `{{${key}}}`);
  });
  return content;
});

const tags = computed(() => {
  return tagsInput.value
    .split(',')
    .map(tag => tag.trim())
    .filter(tag => tag.length > 0);
});

// Methods
const formatVariableName = (variable: string): string => {
  return variable
    .replace(/([A-Z])/g, ' $1')
    .replace(/^./, str => str.toUpperCase())
    .trim();
};

const loadTemplate = () => {
  if (!props.template) return;
  
  form.value.subject = props.template.subject;
  form.value.content = props.template.content;
  
  // Initialize variables
  templateVariables.value.forEach(variable => {
    variables.value[variable] = '';
  });
};

const handleSubmit = async () => {
  isSubmitting.value = true;
  
  try {
    const emailData = {
      from: form.value.from,
      subject: processedSubject.value,
      content: processedContent.value,
      priority: form.value.priority,
      tags: [...tags.value, 'template', props.template?.category.toLowerCase() || ''].filter(Boolean),
      columnId: props.columnId,
      templateId: props.template?.id
    };
    
    emit('create', emailData);
    resetForm();
  } catch (error) {
    console.error('Error creating email from template:', error);
  } finally {
    isSubmitting.value = false;
  }
};

const resetForm = () => {
  form.value = {
    from: '',
    subject: '',
    content: '',
    priority: 'Medium'
  };
  variables.value = {};
  tagsInput.value = '';
};

const closeModal = () => {
  resetForm();
  emit('close');
};

// Watch for template changes
watch(() => props.template, (newTemplate) => {
  if (newTemplate && props.isOpen) {
    loadTemplate();
  }
}, { immediate: true });

// Watch for modal open/close
watch(() => props.isOpen, (isOpen) => {
  if (isOpen && props.template) {
    loadTemplate();
  } else if (!isOpen) {
    resetForm();
  }
});
</script>
