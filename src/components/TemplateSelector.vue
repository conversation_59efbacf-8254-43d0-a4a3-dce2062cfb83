<template>
  <div
    v-if="isOpen"
    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
    @click.self="closeModal"
  >
    <div class="bg-white rounded-lg shadow-xl w-full max-w-4xl mx-4 max-h-[90vh] overflow-hidden">
      <!-- Header -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200">
        <div class="flex items-center space-x-3">
          <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
            <FileText class="w-4 h-4 text-purple-600" />
          </div>
          <div>
            <h2 class="text-lg font-semibold text-gray-900">Email Templates</h2>
            <p class="text-sm text-gray-500">Choose a template for {{ columnTitle }}</p>
          </div>
        </div>
        <button
          @click="closeModal"
          class="p-2 hover:bg-gray-100 rounded-lg transition-colors"
        >
          <X class="w-5 h-5 text-gray-400" />
        </button>
      </div>

      <!-- Content -->
      <div class="p-6">
        <!-- Search -->
        <div class="mb-6">
          <div class="relative">
            <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Search templates..."
              class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
            />
          </div>
        </div>

        <!-- Template Categories -->
        <div class="mb-6">
          <div class="flex flex-wrap gap-2">
            <button
              v-for="category in categories"
              :key="category"
              @click="selectedCategory = category"
              :class="[
                'px-3 py-1 text-sm rounded-full transition-colors',
                selectedCategory === category
                  ? 'bg-purple-100 text-purple-700 border border-purple-200'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              ]"
            >
              {{ category }}
            </button>
          </div>
        </div>

        <!-- Templates Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-96 overflow-y-auto">
          <div
            v-for="template in filteredTemplates"
            :key="template.id"
            @click="selectTemplate(template)"
            class="border border-gray-200 rounded-lg p-4 hover:border-purple-300 hover:shadow-md transition-all cursor-pointer group"
          >
            <div class="flex items-start justify-between mb-3">
              <div class="flex-1">
                <h3 class="font-medium text-gray-900 group-hover:text-purple-700 transition-colors">
                  {{ template.name }}
                </h3>
                <p class="text-sm text-gray-500 mt-1">{{ template.description }}</p>
              </div>
              <span class="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                {{ template.category }}
              </span>
            </div>
            
            <div class="text-sm text-gray-600 mb-3">
              <p class="font-medium">Subject: {{ template.subject }}</p>
            </div>
            
            <div class="text-xs text-gray-500 bg-gray-50 p-2 rounded max-h-20 overflow-hidden">
              {{ template.content.substring(0, 100) }}...
            </div>
            
            <div class="flex items-center justify-between mt-3 pt-3 border-t border-gray-100">
              <div class="flex items-center space-x-2">
                <Clock class="w-3 h-3 text-gray-400" />
                <span class="text-xs text-gray-500">{{ template.lastUsed || 'Never used' }}</span>
              </div>
              <button
                @click.stop="previewTemplate(template)"
                class="text-xs text-purple-600 hover:text-purple-700 transition-colors"
              >
                Preview
              </button>
            </div>
          </div>
        </div>

        <!-- Empty State -->
        <div v-if="filteredTemplates.length === 0" class="text-center py-12">
          <FileText class="w-12 h-12 text-gray-300 mx-auto mb-4" />
          <h3 class="text-lg font-medium text-gray-900 mb-2">No templates found</h3>
          <p class="text-gray-500 mb-4">Try adjusting your search or category filter</p>
          <button
            @click="createNewTemplate"
            class="inline-flex items-center space-x-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
          >
            <Plus class="w-4 h-4" />
            <span>Create New Template</span>
          </button>
        </div>
      </div>

      <!-- Footer -->
      <div class="flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50">
        <button
          @click="createNewTemplate"
          class="inline-flex items-center space-x-2 px-4 py-2 text-purple-600 border border-purple-300 rounded-lg hover:bg-purple-50 transition-colors"
        >
          <Plus class="w-4 h-4" />
          <span>Create New Template</span>
        </button>
        
        <div class="flex items-center space-x-3">
          <button
            @click="closeModal"
            class="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          <button
            v-if="selectedTemplate"
            @click="useTemplate"
            class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
          >
            Use Template
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Preview Modal -->
  <div
    v-if="showPreview && previewingTemplate"
    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-60"
    @click.self="closePreview"
  >
    <div class="bg-white rounded-lg shadow-xl w-full max-w-2xl mx-4 max-h-[80vh] overflow-hidden">
      <div class="flex items-center justify-between p-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold">Template Preview</h3>
        <button @click="closePreview" class="p-2 hover:bg-gray-100 rounded-lg">
          <X class="w-4 h-4 text-gray-400" />
        </button>
      </div>
      <div class="p-6 overflow-y-auto">
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Subject</label>
            <div class="p-3 bg-gray-50 rounded-lg">{{ previewingTemplate.subject }}</div>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Content</label>
            <div class="p-3 bg-gray-50 rounded-lg whitespace-pre-wrap">{{ previewingTemplate.content }}</div>
          </div>
        </div>
      </div>
      <div class="flex justify-end space-x-3 p-4 border-t border-gray-200">
        <button @click="closePreview" class="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50">
          Close
        </button>
        <button @click="usePreviewedTemplate" class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700">
          Use This Template
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { FileText, X, Search, Clock, Plus } from 'lucide-vue-next';

interface EmailTemplate {
  id: string;
  name: string;
  description: string;
  subject: string;
  content: string;
  category: string;
  lastUsed?: string;
  variables?: string[];
}

interface Props {
  isOpen: boolean;
  columnId: string;
  columnTitle: string;
}

interface Emits {
  (e: 'close'): void;
  (e: 'select', template: EmailTemplate): void;
  (e: 'create-new'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// State
const searchQuery = ref('');
const selectedCategory = ref('All');
const selectedTemplate = ref<EmailTemplate | null>(null);
const showPreview = ref(false);
const previewingTemplate = ref<EmailTemplate | null>(null);

// Mock templates data
const templates = ref<EmailTemplate[]>([
  {
    id: '1',
    name: 'Welcome Email',
    description: 'Standard welcome message for new customers',
    subject: 'Welcome to our service!',
    content: 'Dear {{customerName}},\n\nWelcome to our service! We\'re excited to have you on board.\n\nBest regards,\nThe Team',
    category: 'Welcome',
    lastUsed: '2 days ago'
  },
  {
    id: '2',
    name: 'Follow-up',
    description: 'General follow-up email template',
    subject: 'Following up on your inquiry',
    content: 'Hi {{customerName}},\n\nI wanted to follow up on your recent inquiry about {{topic}}.\n\nPlease let me know if you have any questions.\n\nBest regards,\n{{senderName}}',
    category: 'Follow-up'
  },
  {
    id: '3',
    name: 'Thank You',
    description: 'Thank you message after purchase',
    subject: 'Thank you for your purchase!',
    content: 'Dear {{customerName}},\n\nThank you for your recent purchase. We appreciate your business!\n\nYour order #{{orderNumber}} will be processed shortly.\n\nBest regards,\nThe Team',
    category: 'Thank You'
  },
  {
    id: '4',
    name: 'Support Response',
    description: 'Standard support ticket response',
    subject: 'Re: Support Ticket #{{ticketNumber}}',
    content: 'Hi {{customerName}},\n\nThank you for contacting our support team.\n\nWe have received your request and will get back to you within 24 hours.\n\nTicket #{{ticketNumber}}\n\nBest regards,\nSupport Team',
    category: 'Support'
  }
]);

const categories = computed(() => {
  const cats = ['All', ...new Set(templates.value.map(t => t.category))];
  return cats;
});

const filteredTemplates = computed(() => {
  let filtered = templates.value;
  
  if (selectedCategory.value !== 'All') {
    filtered = filtered.filter(t => t.category === selectedCategory.value);
  }
  
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    filtered = filtered.filter(t => 
      t.name.toLowerCase().includes(query) ||
      t.description.toLowerCase().includes(query) ||
      t.subject.toLowerCase().includes(query)
    );
  }
  
  return filtered;
});

// Methods
const selectTemplate = (template: EmailTemplate) => {
  selectedTemplate.value = template;
};

const useTemplate = () => {
  if (selectedTemplate.value) {
    emit('select', selectedTemplate.value);
    closeModal();
  }
};

const previewTemplate = (template: EmailTemplate) => {
  previewingTemplate.value = template;
  showPreview.value = true;
};

const closePreview = () => {
  showPreview.value = false;
  previewingTemplate.value = null;
};

const usePreviewedTemplate = () => {
  if (previewingTemplate.value) {
    emit('select', previewingTemplate.value);
    closePreview();
    closeModal();
  }
};

const createNewTemplate = () => {
  emit('create-new');
  closeModal();
};

const closeModal = () => {
  selectedTemplate.value = null;
  searchQuery.value = '';
  selectedCategory.value = 'All';
  emit('close');
};

// Reset when modal opens
watch(() => props.isOpen, (isOpen) => {
  if (!isOpen) {
    selectedTemplate.value = null;
    searchQuery.value = '';
    selectedCategory.value = 'All';
  }
});
</script>
