import type { <PERSON><PERSON><PERSON><PERSON>, Column, Team } from '@/types';

export const mockTeam: Team[] = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: 'https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&dpr=2',
    role: 'admin',
    status: 'online'
  },
  {
    id: '2',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&dpr=2',
    role: 'manager',
    status: 'online'
  },
  {
    id: '3',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: 'https://images.pexels.com/photos/2182970/pexels-photo-2182970.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&dpr=2',
    role: 'agent',
    status: 'away'
  }
];

export const mockEmails: EmailCard[] = [
  {
    id: '1',
    subject: 'Question about AI features in EmailFlow',
    from: '<EMAIL>',
    to: '<EMAIL>',
    content: 'Hello, I would like to know more details about the AI features available in EmailFlow. Can it be customized for our business needs?',
    timestamp: new Date('2025-01-08T10:30:00'),
    priority: 'high',
    sentiment: 'positive',
    category: 'Product Inquiry',
    assignedTo: '2',
    tags: ['AI', 'Features', 'Business'],
    attachments: 0,
    isRead: false,
    aiSummary: 'Customer asking about AI feature customization for their business'
  },
  {
    id: '2',
    subject: 'EmailFlow demo for our team',
    from: '<EMAIL>',
    to: '<EMAIL>',
    content: 'We are interested in using EmailFlow for our customer service team of 15 people. Could we schedule a demo?',
    timestamp: new Date('2025-01-08T09:15:00'),
    priority: 'high',
    sentiment: 'positive',
    category: 'Sales',
    assignedTo: '1',
    tags: ['Demo', 'Team', 'Sales'],
    attachments: 1,
    isRead: true,
    followUpDate: new Date('2025-01-10T14:00:00')
  },
  {
    id: '3',
    subject: 'Gmail synchronization issue',
    from: '<EMAIL>',
    to: '<EMAIL>',
    content: 'My Gmail emails are not showing up in the EmailFlow board since yesterday. I tried reconnecting but it\'s still the same. Please help.',
    timestamp: new Date('2025-01-08T08:45:00'),
    priority: 'medium',
    sentiment: 'negative',
    category: 'Technical Support',
    assignedTo: '3',
    tags: ['Gmail', 'Sync', 'Bug'],
    attachments: 2,
    isRead: true,
    aiSummary: 'Gmail sync issue, user tried reconnecting but problem persists'
  },
  {
    id: '4',
    subject: 'Positive feedback for new features',
    from: '<EMAIL>',
    to: '<EMAIL>',
    content: 'Our team is very happy with the latest AI feature update! The automatic priority detection is very helpful. Thank you EmailFlow!',
    timestamp: new Date('2025-01-08T07:20:00'),
    priority: 'low',
    sentiment: 'positive',
    category: 'Feedback',
    tags: ['Positive', 'AI', 'Feature'],
    attachments: 0,
    isRead: true,
    aiSummary: 'Positive feedback about new AI features, especially priority detection'
  },
  {
    id: '5',
    subject: 'EmailFlow license renewal',
    from: '<EMAIL>',
    to: '<EMAIL>',
    content: 'Our license will expire next month. How do we renew it? We want to upgrade to the Pro plan.',
    timestamp: new Date('2025-01-08T11:10:00'),
    priority: 'medium',
    sentiment: 'neutral',
    category: 'Billing',
    assignedTo: '1',
    tags: ['License', 'Renewal', 'Upgrade'],
    attachments: 0,
    isRead: false,
    followUpDate: new Date('2025-01-15T10:00:00')
  }
];

export const initialColumns: Column[] = [
  {
    id: 'inbox',
    title: 'Inbox',
    color: 'bg-blue-50 border-blue-200',
    cards: [mockEmails[0], mockEmails[4]],
    aiEnabled: true
  },
  {
    id: 'needs-review',
    title: 'Needs Review',
    color: 'bg-yellow-50 border-yellow-200',
    cards: [mockEmails[2]],
    aiEnabled: true
  },
  {
    id: 'awaiting-reply',
    title: 'Awaiting Reply',
    color: 'bg-purple-50 border-purple-200',
    cards: [mockEmails[1]],
    aiEnabled: false
  },
  {
    id: 'follow-up',
    title: 'Follow Up',
    color: 'bg-orange-50 border-orange-200',
    cards: [],
    aiEnabled: true
  },
  {
    id: 'resolved',
    title: 'Resolved',
    color: 'bg-green-50 border-green-200',
    cards: [mockEmails[3]],
    aiEnabled: false
  }
];