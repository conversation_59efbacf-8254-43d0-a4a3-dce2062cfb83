import { Email<PERSON><PERSON>, Column, Team } from '../types';

export const mockTeam: Team[] = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: 'https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&dpr=2',
    role: 'admin',
    status: 'online'
  },
  {
    id: '2',
    name: '<PERSON><PERSON>',
    email: '<EMAIL>',
    avatar: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&dpr=2',
    role: 'manager',
    status: 'online'
  },
  {
    id: '3',
    name: '<PERSON><PERSON>',
    email: '<EMAIL>',
    avatar: 'https://images.pexels.com/photos/2182970/pexels-photo-2182970.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&dpr=2',
    role: 'agent',
    status: 'away'
  }
];

export const mockEmails: EmailCard[] = [
  {
    id: '1',
    subject: 'Pertanyaan tentang fitur AI KirimPintar',
    from: '<EMAIL>',
    to: '<EMAIL>',
    content: 'Halo, saya ingin tahu lebih detail tentang fitur AI yang ada di KirimPintar. Apakah bisa disesuaikan dengan kebutuhan bisnis kami?',
    timestamp: new Date('2025-01-08T10:30:00'),
    priority: 'high',
    sentiment: 'positive',
    category: 'Product Inquiry',
    assignedTo: '2',
    tags: ['AI', 'Features', 'Business'],
    attachments: 0,
    isRead: false,
    aiSummary: 'Pelanggan bertanya tentang kustomisasi fitur AI untuk bisnis mereka'
  },
  {
    id: '2',
    subject: 'Demo KirimPintar untuk tim kami',
    from: '<EMAIL>',
    to: '<EMAIL>',
    content: 'Kami tertarik untuk menggunakan KirimPintar untuk tim customer service kami yang berjumlah 15 orang. Bisakah dijadwalkan demo?',
    timestamp: new Date('2025-01-08T09:15:00'),
    priority: 'high',
    sentiment: 'positive',
    category: 'Sales',
    assignedTo: '1',
    tags: ['Demo', 'Team', 'Sales'],
    attachments: 1,
    isRead: true,
    followUpDate: new Date('2025-01-10T14:00:00')
  },
  {
    id: '3',
    subject: 'Masalah sinkronisasi email Gmail',
    from: '<EMAIL>',
    to: '<EMAIL>',
    content: 'Email dari Gmail saya tidak muncul di papan KirimPintar sejak kemarin. Sudah coba reconnect tapi masih sama. Mohon bantuannya.',
    timestamp: new Date('2025-01-08T08:45:00'),
    priority: 'medium',
    sentiment: 'negative',
    category: 'Technical Support',
    assignedTo: '3',
    tags: ['Gmail', 'Sync', 'Bug'],
    attachments: 2,
    isRead: true,
    aiSummary: 'Masalah sinkronisasi Gmail, sudah coba reconnect namun belum berhasil'
  },
  {
    id: '4',
    subject: 'Feedback positif untuk fitur baru',
    from: '<EMAIL>',
    to: '<EMAIL>',
    content: 'Tim kami sangat senang dengan update fitur AI terbaru! Deteksi prioritas otomatisnya sangat membantu. Terima kasih KirimPintar!',
    timestamp: new Date('2025-01-08T07:20:00'),
    priority: 'low',
    sentiment: 'positive',
    category: 'Feedback',
    tags: ['Positive', 'AI', 'Feature'],
    attachments: 0,
    isRead: true,
    aiSummary: 'Feedback positif tentang fitur AI baru, khususnya deteksi prioritas'
  },
  {
    id: '5',
    subject: 'Perpanjangan lisensi KirimPintar',
    from: '<EMAIL>',
    to: '<EMAIL>',
    content: 'Lisensi kami akan berakhir bulan depan. Bagaimana cara perpanjangannya? Kami ingin upgrade ke paket Pro.',
    timestamp: new Date('2025-01-08T11:10:00'),
    priority: 'medium',
    sentiment: 'neutral',
    category: 'Billing',
    assignedTo: '1',
    tags: ['License', 'Renewal', 'Upgrade'],
    attachments: 0,
    isRead: false,
    followUpDate: new Date('2025-01-15T10:00:00')
  }
];

export const initialColumns: Column[] = [
  {
    id: 'inbox',
    title: 'Inbox',
    color: 'bg-blue-50 border-blue-200',
    cards: [mockEmails[0], mockEmails[4]],
    aiEnabled: true
  },
  {
    id: 'needs-review',
    title: 'Needs Review',
    color: 'bg-yellow-50 border-yellow-200',
    cards: [mockEmails[2]],
    aiEnabled: true
  },
  {
    id: 'awaiting-reply',
    title: 'Awaiting Reply',
    color: 'bg-purple-50 border-purple-200',
    cards: [mockEmails[1]],
    aiEnabled: false
  },
  {
    id: 'follow-up',
    title: 'Follow Up',
    color: 'bg-orange-50 border-orange-200',
    cards: [],
    aiEnabled: true
  },
  {
    id: 'resolved',
    title: 'Resolved',
    color: 'bg-green-50 border-green-200',
    cards: [mockEmails[3]],
    aiEnabled: false
  }
];