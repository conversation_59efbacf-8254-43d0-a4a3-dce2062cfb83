import { createRouter, createWebHistory } from 'vue-router';
import BoardView from '@/views/BoardView.vue';
import AnalyticsView from '@/views/AnalyticsView.vue';
import TeamView from '@/views/TeamView.vue';

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'board',
      component: BoardView,
    },
    {
      path: '/analytics',
      name: 'analytics',
      component: AnalyticsView,
    },
    {
      path: '/team',
      name: 'team',
      component: TeamView,
    },
    // Placeholder routes for other menu items
    {
      path: '/templates',
      name: 'templates',
      component: () => import('@/views/PlaceholderView.vue'),
    },
    {
      path: '/automation',
      name: 'automation',
      component: () => import('@/views/PlaceholderView.vue'),
    },
    {
      path: '/calendar',
      name: 'calendar',
      component: () => import('@/views/PlaceholderView.vue'),
    },
    {
      path: '/archive',
      name: 'archive',
      component: () => import('@/views/PlaceholderView.vue'),
    },
    {
      path: '/bookmarks',
      name: 'bookmarks',
      component: () => import('@/views/PlaceholderView.vue'),
    },
  ],
});

export default router;
