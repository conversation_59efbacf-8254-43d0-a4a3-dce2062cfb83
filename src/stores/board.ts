import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import type { Column, EmailCard } from '@/types';
import { initialColumns } from '@/data/mockData';

export const useBoardStore = defineStore('board', () => {
  const columns = ref<Column[]>(initialColumns);
  const draggedEmailId = ref<string | null>(null);
  const selectedEmail = ref<EmailCard | null>(null);
  const selectedEmails = ref<Set<string>>(new Set());
  const isMultiSelectMode = ref(false);
  const filters = ref({
    priority: '',
    assignee: '',
    search: '',
    tags: [] as string[]
  });
  const sortBy = ref<'date' | 'priority' | 'assignee'>('date');
  const sortOrder = ref<'asc' | 'desc'>('desc');

  // Computed properties
  const totalEmails = computed(() => {
    return columns.value.reduce((total, column) => total + column.cards.length, 0);
  });

  const emailsByPriority = computed(() => {
    const emails = getAllEmails();
    return {
      high: emails.filter(email => email.priority === 'high').length,
      medium: emails.filter(email => email.priority === 'medium').length,
      low: emails.filter(email => email.priority === 'low').length
    };
  });

  // Helper functions
  const getAllEmails = (): EmailCard[] => {
    return columns.value.flatMap(column => column.cards);
  };

  const findEmailById = (emailId: string): { email: EmailCard; columnId: string } | null => {
    for (const column of columns.value) {
      const email = column.cards.find(card => card.id === emailId);
      if (email) {
        return { email, columnId: column.id };
      }
    }
    return null;
  };

  // Email operations
  const moveEmail = (emailId: string, targetColumnId: string, targetIndex?: number) => {
    let draggedEmail: EmailCard | null = null;
    let sourceColumnId: string | null = null;

    // Find and remove the dragged email from its current column
    for (const column of columns.value) {
      const emailIndex = column.cards.findIndex(card => card.id === emailId);
      if (emailIndex !== -1) {
        draggedEmail = column.cards[emailIndex];
        sourceColumnId = column.id;
        column.cards.splice(emailIndex, 1);
        break;
      }
    }

    // Add the email to the target column
    if (draggedEmail) {
      const targetColumn = columns.value.find(col => col.id === targetColumnId);
      if (targetColumn) {
        if (targetIndex !== undefined) {
          targetColumn.cards.splice(targetIndex, 0, draggedEmail);
        } else {
          targetColumn.cards.push(draggedEmail);
        }

        // Update email status based on column
        updateEmailStatus(draggedEmail, targetColumnId);
      }
    }

    return { draggedEmail, sourceColumnId, targetColumnId };
  };

  const updateEmailStatus = (email: EmailCard, columnId: string) => {
    switch (columnId) {
      case 'new':
        email.isRead = false;
        break;
      case 'in-progress':
        email.isRead = true;
        break;
      case 'waiting':
        email.isRead = true;
        break;
      case 'resolved':
        email.isRead = true;
        break;
    }
  };

  const addEmail = (columnId: string, email: Omit<EmailCard, 'id'>) => {
    const newEmail: EmailCard = {
      ...email,
      id: Date.now().toString(),
      timestamp: new Date()
    };

    const column = columns.value.find(col => col.id === columnId);
    if (column) {
      column.cards.unshift(newEmail);
    }

    return newEmail;
  };

  const deleteEmail = (emailId: string) => {
    for (const column of columns.value) {
      const emailIndex = column.cards.findIndex(card => card.id === emailId);
      if (emailIndex !== -1) {
        const deletedEmail = column.cards[emailIndex];
        column.cards.splice(emailIndex, 1);
        selectedEmails.value.delete(emailId);
        return deletedEmail;
      }
    }
    return null;
  };

  const updateEmail = (emailId: string, updates: Partial<EmailCard>) => {
    const result = findEmailById(emailId);
    if (result) {
      Object.assign(result.email, updates);
      return result.email;
    }
    return null;
  };

  // Selection operations
  const toggleEmailSelection = (emailId: string) => {
    if (selectedEmails.value.has(emailId)) {
      selectedEmails.value.delete(emailId);
    } else {
      selectedEmails.value.add(emailId);
    }
  };

  const selectAllEmails = () => {
    const allEmailIds = getAllEmails().map(email => email.id);
    selectedEmails.value = new Set(allEmailIds);
  };

  const clearSelection = () => {
    selectedEmails.value.clear();
    isMultiSelectMode.value = false;
  };

  // Bulk operations
  const bulkMoveEmails = (emailIds: string[], targetColumnId: string) => {
    const results = [];
    for (const emailId of emailIds) {
      const result = moveEmail(emailId, targetColumnId);
      results.push(result);
    }
    clearSelection();
    return results;
  };

  const bulkDeleteEmails = (emailIds: string[]) => {
    const deletedEmails = [];
    for (const emailId of emailIds) {
      const deleted = deleteEmail(emailId);
      if (deleted) deletedEmails.push(deleted);
    }
    clearSelection();
    return deletedEmails;
  };

  const bulkAssignEmails = (emailIds: string[], assigneeId: string) => {
    const updatedEmails = [];
    for (const emailId of emailIds) {
      const updated = updateEmail(emailId, { assignedTo: assigneeId });
      if (updated) updatedEmails.push(updated);
    }
    clearSelection();
    return updatedEmails;
  };

  // State setters
  const setDraggedEmailId = (emailId: string | null) => {
    draggedEmailId.value = emailId;
  };

  const setSelectedEmail = (email: EmailCard | null) => {
    selectedEmail.value = email;
  };

  const setMultiSelectMode = (enabled: boolean) => {
    isMultiSelectMode.value = enabled;
    if (!enabled) {
      clearSelection();
    }
  };

  const setFilters = (newFilters: Partial<typeof filters.value>) => {
    Object.assign(filters.value, newFilters);
  };

  const setSorting = (field: typeof sortBy.value, order: typeof sortOrder.value) => {
    sortBy.value = field;
    sortOrder.value = order;
  };

  return {
    // State
    columns,
    draggedEmailId,
    selectedEmail,
    selectedEmails,
    isMultiSelectMode,
    filters,
    sortBy,
    sortOrder,

    // Computed
    totalEmails,
    emailsByPriority,

    // Methods
    getAllEmails,
    findEmailById,
    moveEmail,
    addEmail,
    deleteEmail,
    updateEmail,
    toggleEmailSelection,
    selectAllEmails,
    clearSelection,
    bulkMoveEmails,
    bulkDeleteEmails,
    bulkAssignEmails,
    setDraggedEmailId,
    setSelectedEmail,
    setMultiSelectMode,
    setFilters,
    setSorting,
  };
});
