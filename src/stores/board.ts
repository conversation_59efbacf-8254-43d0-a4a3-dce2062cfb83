import { defineStore } from 'pinia';
import { ref } from 'vue';
import type { Column, EmailCard } from '@/types';
import { initialColumns } from '@/data/mockData';

export const useBoardStore = defineStore('board', () => {
  const columns = ref<Column[]>(initialColumns);
  const draggedEmailId = ref<string | null>(null);
  const selectedEmail = ref<EmailCard | null>(null);

  const moveEmail = (emailId: string, targetColumnId: string) => {
    let draggedEmail: EmailCard | null = null;
    
    // Find and remove the dragged email from its current column
    for (const column of columns.value) {
      const emailIndex = column.cards.findIndex(card => card.id === emailId);
      if (emailIndex !== -1) {
        draggedEmail = column.cards[emailIndex];
        column.cards.splice(emailIndex, 1);
        break;
      }
    }

    // Add the email to the target column
    if (draggedEmail) {
      const targetColumn = columns.value.find(col => col.id === targetColumnId);
      if (targetColumn) {
        targetColumn.cards.push(draggedEmail);
      }
    }
  };

  const setDraggedEmailId = (emailId: string | null) => {
    draggedEmailId.value = emailId;
  };

  const setSelectedEmail = (email: EmailCard | null) => {
    selectedEmail.value = email;
  };

  return {
    columns,
    draggedEmailId,
    selectedEmail,
    moveEmail,
    setDraggedEmailId,
    setSelectedEmail,
  };
});
