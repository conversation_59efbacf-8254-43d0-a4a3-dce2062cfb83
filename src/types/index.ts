export interface EmailCard {
  id: string;
  subject: string;
  from: string;
  to: string;
  content: string;
  timestamp: Date;
  priority: 'high' | 'medium' | 'low';
  sentiment: 'positive' | 'neutral' | 'negative';
  category: string;
  assignedTo?: string;
  tags: string[];
  attachments: number;
  isRead: boolean;
  aiSummary?: string;
  followUpDate?: Date;
}

export interface Column {
  id: string;
  title: string;
  color: string;
  cards: EmailCard[];
  aiEnabled: boolean;
}

export interface Team {
  id: string;
  name: string;
  email: string;
  avatar: string;
  role: 'admin' | 'manager' | 'agent';
  status: 'online' | 'away' | 'offline';
}

export interface AIInsight {
  type: 'priority' | 'category' | 'sentiment' | 'summary';
  confidence: number;
  suggestion: string;
}