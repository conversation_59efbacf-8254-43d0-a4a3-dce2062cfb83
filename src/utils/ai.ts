import { EmailCard } from '../types';

export const getPriorityColor = (priority: string) => {
  switch (priority) {
    case 'high':
      return 'bg-red-100 text-red-800 border-red-200';
    case 'medium':
      return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    case 'low':
      return 'bg-green-100 text-green-800 border-green-200';
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200';
  }
};

export const getSentimentIcon = (sentiment: string) => {
  switch (sentiment) {
    case 'positive':
      return '😊';
    case 'negative':
      return '😞';
    case 'neutral':
    default:
      return '😐';
  }
};

export const getSentimentColor = (sentiment: string) => {
  switch (sentiment) {
    case 'positive':
      return 'text-green-600';
    case 'negative':
      return 'text-red-600';
    case 'neutral':
    default:
      return 'text-gray-600';
  }
};

export const formatTimeAgo = (date: Date): string => {
  const now = new Date();
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
  
  if (diffInMinutes < 1) return 'Baru saja';
  if (diffInMinutes < 60) return `${diffInMinutes} menit lalu`;
  
  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) return `${diffInHours} jam lalu`;
  
  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 7) return `${diffInDays} hari lalu`;
  
  return date.toLocaleDateString('id-ID');
};

export const generateAIInsight = (email: EmailCard) => {
  const insights = [
    'AI menyarankan prioritas tinggi berdasarkan kata kunci "urgent"',
    'Terdeteksi sebagai pertanyaan penjualan - dialihkan ke tim sales',
    'Sentimen positif terdeteksi - pelanggan puas dengan layanan',
    'Email mengandung keluhan - perlu perhatian khusus',
    'Follow up diperlukan dalam 24 jam'
  ];
  
  return insights[Math.floor(Math.random() * insights.length)];
};