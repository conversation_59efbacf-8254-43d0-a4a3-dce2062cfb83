<template>
  <div class="flex-1 p-6">
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-900 mb-2"><PERSON><PERSON>ail</h1>
      <p class="text-gray-600"><PERSON><PERSON><PERSON> semua email pelanggan dengan sistem visual yang mudah</p>
    </div>
    
    <!-- AI Status Bar -->
    <div class="bg-gradient-to-r from-blue-50 to-teal-50 border border-blue-200 rounded-lg p-4 mb-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-3">
          <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
          <span class="font-medium text-gray-900">AI Assistant Aktif</span>
          <span class="text-sm text-gray-600">Memproses 5 email baru | Akurasi 94%</span>
        </div>
        <div class="flex items-center space-x-2 text-sm text-gray-600">
          <span>12 insights hari ini</span>
          <span>•</span>
          <span>2.4 jam waktu hemat</span>
        </div>
      </div>
    </div>

    <!-- Kanban Board -->
    <div class="flex space-x-6 overflow-x-auto pb-6 min-h-96">
      <Column
        v-for="column in columns"
        :key="column.id"
        :column="column"
        @drag-over="handleDragOver"
        @drop="handleDrop"
        @drag-start="handleDragStart"
        @email-click="handleEmailClick"
        @add-email="handleAddEmail"
      />
    </div>

    <!-- Quick Stats -->
    <div class="mt-8 grid grid-cols-1 md:grid-cols-4 gap-4">
      <div class="bg-white rounded-lg border border-gray-200 p-4">
        <h3 class="text-sm font-medium text-gray-600 mb-2">Total Email Hari Ini</h3>
        <p class="text-2xl font-bold text-gray-900">23</p>
        <p class="text-xs text-green-600">+15% dari kemarin</p>
      </div>
      <div class="bg-white rounded-lg border border-gray-200 p-4">
        <h3 class="text-sm font-medium text-gray-600 mb-2">Rata-rata Respon</h3>
        <p class="text-2xl font-bold text-gray-900">1.8 jam</p>
        <p class="text-xs text-green-600">-0.3 jam dari target</p>
      </div>
      <div class="bg-white rounded-lg border border-gray-200 p-4">
        <h3 class="text-sm font-medium text-gray-600 mb-2">Email Resolved</h3>
        <p class="text-2xl font-bold text-gray-900">18</p>
        <p class="text-xs text-blue-600">78% dari total</p>
      </div>
      <div class="bg-white rounded-lg border border-gray-200 p-4">
        <h3 class="text-sm font-medium text-gray-600 mb-2">Kepuasan Pelanggan</h3>
        <p class="text-2xl font-bold text-gray-900">4.8/5</p>
        <p class="text-xs text-green-600">+0.2 dari bulan lalu</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { storeToRefs } from 'pinia';
import { onMounted } from 'vue';
import Column from '@/components/Column.vue';
import { useBoardStore } from '@/stores/board';
import type { EmailCard } from '@/types';

const boardStore = useBoardStore();
const { columns } = storeToRefs(boardStore);

onMounted(() => {
  console.log('BoardView mounted, columns:', columns.value);
});

const handleDragStart = (emailId: string) => {
  boardStore.setDraggedEmailId(emailId);
};

const handleDragOver = (event: DragEvent) => {
  event.preventDefault();
};

const handleDrop = (event: DragEvent, targetColumnId: string) => {
  event.preventDefault();
  
  if (!boardStore.draggedEmailId) return;

  boardStore.moveEmail(boardStore.draggedEmailId, targetColumnId);
  boardStore.setDraggedEmailId(null);
};

const handleEmailClick = (email: EmailCard) => {
  boardStore.setSelectedEmail(email);
  console.log('Email clicked:', email);
};

const handleAddEmail = (columnId: string) => {
  console.log('Add email to column:', columnId);
};
</script>
