<template>
  <div class="flex-1 p-6">
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-900 mb-2">Email Board</h1>
      <p class="text-gray-600">Manage all customer emails with an easy visual system</p>
    </div>
    
    <!-- AI Status Bar -->
    <div class="bg-gradient-to-r from-blue-50 to-teal-50 border border-blue-200 rounded-lg p-4 mb-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-3">
          <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
          <span class="font-medium text-gray-900">AI Assistant Active</span>
          <span class="text-sm text-gray-600">Processing 5 new emails | 94% accuracy</span>
        </div>
        <div class="flex items-center space-x-2 text-sm text-gray-600">
          <span>12 insights today</span>
          <span>•</span>
          <span>2.4 hours saved</span>
        </div>
      </div>
    </div>

    <!-- Filters and Controls -->
    <div class="mb-6 flex flex-wrap items-center justify-between gap-4">
      <div class="flex items-center space-x-4">
        <!-- Search -->
        <div class="relative">
          <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Search emails..."
            class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent w-64"
          />
        </div>

        <!-- Priority Filter -->
        <select
          v-model="priorityFilter"
          class="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="">All Priorities</option>
          <option value="high">High Priority</option>
          <option value="medium">Medium Priority</option>
          <option value="low">Low Priority</option>
        </select>

        <!-- Assignee Filter -->
        <select
          v-model="assigneeFilter"
          class="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="">All Assignees</option>
          <option value="">Unassigned</option>
          <option v-for="member in team" :key="member.id" :value="member.id">
            {{ member.name }}
          </option>
        </select>
      </div>

      <div class="flex items-center space-x-2">
        <!-- Sort Options -->
        <select
          v-model="sortBy"
          class="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="date">Sort by Date</option>
          <option value="priority">Sort by Priority</option>
          <option value="assignee">Sort by Assignee</option>
        </select>

        <button
          @click="toggleSortOrder"
          class="p-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          :title="sortOrder === 'desc' ? 'Descending' : 'Ascending'"
        >
          <ArrowUpDown class="w-4 h-4" :class="{ 'rotate-180': sortOrder === 'desc' }" />
        </button>

        <!-- Multi-select Toggle -->
        <button
          @click="toggleMultiSelect"
          class="px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors flex items-center space-x-2"
          :class="{ 'bg-blue-50 border-blue-300 text-blue-700': isMultiSelectMode }"
        >
          <CheckSquare class="w-4 h-4" />
          <span>Select</span>
        </button>

        <!-- Clear Filters -->
        <button
          @click="clearFilters"
          class="px-3 py-2 text-gray-600 hover:text-gray-900 transition-colors"
        >
          Clear Filters
        </button>
      </div>
    </div>

    <!-- Kanban Board -->
    <div class="flex space-x-6 overflow-x-auto pb-6 min-h-96">
      <Column
        v-for="column in columns"
        :key="column.id"
        :column="column"
        @email-click="openEmailDetail"
        @email-right-click="handleEmailRightClick"
        @add-contact="handleAddContact"
        @quick-add-contact="handleQuickAddContact"
        @edit-column="handleEditColumn"
        @open-email-actions="openEmailActions"
        @open-sequence-builder="openSequenceBuilder"
        @open-broadcast-builder="openBroadcastBuilder"
        @open-template-builder="(columnId) => openTemplateBuilder(columnId)"
        @view-column-analytics="viewColumnAnalytics"
        @export-column-data="exportColumnData"
        @import-contacts="openImportDialog"
        @open-column-management="openColumnManagement"
        @open-sequence-builder="openSequenceBuilderFromColumn"
        class="animate-fade-in"
      />
    </div>

    <!-- Quick Stats -->
    <div class="mt-8 grid grid-cols-1 md:grid-cols-4 gap-4">
      <div class="bg-white rounded-lg border border-gray-200 p-4 hover:shadow-lg transition-all duration-200 hover:border-blue-200 animate-fade-in">
        <h3 class="text-sm font-medium text-gray-600 mb-2">Total Emails Today</h3>
        <p class="text-2xl font-bold text-gray-900">23</p>
        <p class="text-xs text-green-600 flex items-center">
          <span class="w-2 h-2 bg-green-500 rounded-full mr-1"></span>
          +15% from yesterday
        </p>
      </div>
      <div class="bg-white rounded-lg border border-gray-200 p-4 hover:shadow-lg transition-all duration-200 hover:border-blue-200 animate-fade-in" style="animation-delay: 100ms">
        <h3 class="text-sm font-medium text-gray-600 mb-2">Average Response</h3>
        <p class="text-2xl font-bold text-gray-900">1.8 hours</p>
        <p class="text-xs text-green-600 flex items-center">
          <span class="w-2 h-2 bg-green-500 rounded-full mr-1"></span>
          -0.3 hours from target
        </p>
      </div>
      <div class="bg-white rounded-lg border border-gray-200 p-4 hover:shadow-lg transition-all duration-200 hover:border-blue-200 animate-fade-in" style="animation-delay: 200ms">
        <h3 class="text-sm font-medium text-gray-600 mb-2">Emails Resolved</h3>
        <p class="text-2xl font-bold text-gray-900">18</p>
        <p class="text-xs text-blue-600 flex items-center">
          <span class="w-2 h-2 bg-blue-500 rounded-full mr-1"></span>
          78% of total
        </p>
      </div>
      <div class="bg-white rounded-lg border border-gray-200 p-4 hover:shadow-lg transition-all duration-200 hover:border-blue-200 animate-fade-in" style="animation-delay: 300ms">
        <h3 class="text-sm font-medium text-gray-600 mb-2">Customer Satisfaction</h3>
        <p class="text-2xl font-bold text-gray-900">4.8/5</p>
        <p class="text-xs text-green-600 flex items-center">
          <span class="w-2 h-2 bg-green-500 rounded-full mr-1"></span>
          +0.2 from last month
        </p>
      </div>
    </div>

    <!-- Add Email Modal -->
    <AddEmailModal
      :is-open="showAddEmailModal"
      :column-id="selectedColumnId"
      :column-title="selectedColumnTitle"
      @close="closeAddEmailModal"
      @create-manual="openManualEmailForm"
      @open-form-builder="openFormBuilder"
      @open-integration-hub="openIntegrationHub"
      @open-import-dialog="openImportDialog"
    />

    <!-- Manual Email Form -->
    <ManualEmailForm
      :is-open="showManualEmailForm"
      :column-id="selectedColumnId"
      @close="closeManualEmailForm"
      @create="handleManualEmailCreate"
    />

    <!-- Quick Add Contact Modal -->
    <QuickAddContactModal
      :is-open="showQuickAddModal"
      :column-id="selectedColumnId"
      :column-title="selectedColumnTitle"
      @close="closeQuickAddModal"
      @create="handleQuickContactCreate"
    />

    <!-- Template Selector Modal -->
    <TemplateSelector
      :is-open="showTemplateSelector"
      :column-id="selectedColumnId"
      :column-title="selectedColumnTitle"
      @close="closeTemplateSelector"
      @select="handleTemplateSelect"
      @create-new="() => console.log('Create new template')"
    />

    <!-- Template Email Form Modal -->
    <TemplateEmailForm
      :is-open="showTemplateEmailForm"
      :template="selectedTemplate"
      :column-id="selectedColumnId"
      :column-title="selectedColumnTitle"
      @close="closeTemplateEmailForm"
      @create="handleTemplateEmailCreate"
    />

    <!-- Contact Import Modal -->
    <ContactImportModal
      :is-open="showImportModal"
      :column-id="selectedColumnId"
      :column-title="selectedColumnTitle"
      @close="closeImportModal"
      @import="handleContactImport"
    />

    <!-- Column Management Dashboard -->
    <ColumnManagementDashboard
      :is-open="showColumnManagement"
      :column-id="selectedColumnId"
      :column-title="selectedColumnTitle"
      @close="closeColumnManagement"
      @create-sequence="openSequenceBuilder"
      @create-broadcast="openBroadcastBuilder"
      @edit-contact="handleEditContact"
    />

    <!-- Sequence Management Modal -->
    <SequenceManagementModal
      :is-open="showSequenceManagement"
      :sequence="selectedSequence"
      :column-title="selectedColumnTitle"
      @close="closeSequenceManagement"
      @edit-sequence="handleEditSequence"
      @enroll-contacts="handleEnrollContacts"
    />

    <!-- Broadcast Management Modal -->
    <BroadcastManagementModal
      :is-open="showBroadcastManagement"
      :broadcast="selectedBroadcast"
      :column-title="selectedColumnTitle"
      @close="closeBroadcastManagement"
      @edit-broadcast="handleEditBroadcast"
      @send-broadcast="handleSendBroadcast"
      @schedule-broadcast="handleScheduleBroadcast"
    />

    <!-- One-Page Sequence Builder -->
    <OnePageSequenceBuilder
      :is-open="showOnePageSequenceBuilder"
      :sequence="selectedSequence"
      :column-title="selectedColumnTitle"
      @close="closeOnePageSequenceBuilder"
      @save="handleSequenceSave"
    />

    <!-- Email Actions Modal -->
    <EmailActionsModal
      :is-open="showEmailActionsModal"
      :email="selectedEmail"
      @close="closeEmailActionsModal"
      @action-created="handleActionCreated"
    />

    <!-- Email Detail Modal -->
    <EmailDetailModal
      :email="selectedEmail"
      :is-open="showEmailDetail"
      @close="closeEmailDetail"
      @reply="handleEmailReply"
      @forward="handleEmailForward"
      @delete="handleEmailDelete"
    />

    <!-- Bulk Actions Toolbar -->
    <BulkActionsToolbar />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { Search, ArrowUpDown, CheckSquare } from 'lucide-vue-next';
import Column from '@/components/Column.vue';
import EmailDetailModal from '@/components/EmailDetailModal.vue';
import BulkActionsToolbar from '@/components/BulkActionsToolbar.vue';
import AddEmailModal from '@/components/AddEmailModal.vue';
import ManualEmailForm from '@/components/ManualEmailForm.vue';
import EmailActionsModal from '@/components/EmailActionsModal.vue';
import QuickAddContactModal from '@/components/QuickAddEmailModal.vue';
import TemplateSelector from '@/components/TemplateSelector.vue';
import TemplateEmailForm from '@/components/TemplateEmailForm.vue';
import ContactImportModal from '@/components/EmailImportModal.vue';
import ColumnManagementDashboard from '@/components/ColumnManagementDashboard.vue';
import SequenceManagementModal from '@/components/SequenceManagementModal.vue';
import BroadcastManagementModal from '@/components/BroadcastManagementModal.vue';
import OnePageSequenceBuilder from '@/components/OnePageSequenceBuilder.vue';
import { useBoardStore } from '@/stores/board';
import { mockTeam } from '@/data/mockData';
import type { EmailCard } from '@/types';

const boardStore = useBoardStore();
const { columns, isMultiSelectMode, sortBy, sortOrder } = storeToRefs(boardStore);

const team = mockTeam;
const selectedEmail = ref<EmailCard | null>(null);
const showEmailDetail = ref(false);
const showAddEmailModal = ref(false);
const showManualEmailForm = ref(false);
const showEmailActionsModal = ref(false);
const showQuickAddModal = ref(false);
const showTemplateSelector = ref(false);
const showTemplateEmailForm = ref(false);
const showImportModal = ref(false);
const showColumnManagement = ref(false);
const showSequenceManagement = ref(false);
const showBroadcastManagement = ref(false);
const showOnePageSequenceBuilder = ref(false);
const selectedTemplate = ref(null);
const selectedSequence = ref(null);
const selectedBroadcast = ref(null);
const selectedColumnId = ref('');
const selectedColumnTitle = ref('');
const searchQuery = ref('');
const priorityFilter = ref('');
const assigneeFilter = ref('');

// Watch filters and update store
watch(searchQuery, (value) => {
  boardStore.setFilters({ search: value });
});

watch(priorityFilter, (value) => {
  boardStore.setFilters({ priority: value });
});

watch(assigneeFilter, (value) => {
  boardStore.setFilters({ assignee: value });
});

const openEmailDetail = (email: EmailCard) => {
  selectedEmail.value = email;
  showEmailDetail.value = true;
  boardStore.setSelectedEmail(email);
};

const closeEmailDetail = () => {
  showEmailDetail.value = false;
  selectedEmail.value = null;
  boardStore.setSelectedEmail(null);
};

const handleEmailRightClick = (email: EmailCard, event: MouseEvent) => {
  // Show context menu or open email detail
  openEmailDetail(email);
};

const handleAddContact = (columnId: string) => {
  selectedColumnId.value = columnId;
  const column = columns.value.find(col => col.id === columnId);
  selectedColumnTitle.value = column?.title || 'Column';
  showAddEmailModal.value = true;
};

const handleQuickAddContact = (columnId: string) => {
  selectedColumnId.value = columnId;
  const column = columns.value.find(col => col.id === columnId);
  selectedColumnTitle.value = column?.title || 'Column';
  showQuickAddModal.value = true;
};

// Modal handlers
const closeAddEmailModal = () => {
  showAddEmailModal.value = false;
  selectedColumnId.value = '';
  selectedColumnTitle.value = '';
};

const openManualEmailForm = (columnId: string) => {
  selectedColumnId.value = columnId;
  showManualEmailForm.value = true;
};

const closeManualEmailForm = () => {
  showManualEmailForm.value = false;
  selectedColumnId.value = '';
};

const closeQuickAddModal = () => {
  showQuickAddModal.value = false;
  selectedColumnId.value = '';
};

const handleManualEmailCreate = (email: any, columnId: string) => {
  boardStore.addEmail(columnId, email);
};

const handleQuickContactCreate = (contactData: any) => {
  console.log('Creating quick contact:', contactData);
  // TODO: Implement actual contact creation via API
  boardStore.addEmail(contactData.columnId, {
    id: `contact-${Date.now()}`,
    subject: `Contact: ${contactData.name}`,
    from: contactData.email,
    content: `Name: ${contactData.name}\nCompany: ${contactData.company}\nPhone: ${contactData.phone}\nNotes: ${contactData.notes}`,
    priority: contactData.priority,
    tags: contactData.tags,
    date: new Date().toISOString(),
    assignedTo: '',
    isRead: false
  });
  closeQuickAddModal();
};

const openFormBuilder = () => {
  console.log('Opening form builder...');
  // Implement form builder
};

const openIntegrationHub = () => {
  console.log('Opening integration hub...');
  // Implement integration hub
};



const closeEmailActionsModal = () => {
  showEmailActionsModal.value = false;
};

const handleActionCreated = (action: any) => {
  console.log('Action created:', action);
  // Handle different action types
};

const handleEditColumn = (columnId: string) => {
  console.log('Edit column:', columnId);
  // Implement column editing
};

// Column action handlers
const openEmailActions = () => {
  showEmailActionsModal.value = true;
};



const openTemplateBuilder = (columnId: string) => {
  selectedColumnId.value = columnId;
  const column = columns.value.find(col => col.id === columnId);
  selectedColumnTitle.value = column?.title || 'Column';
  showTemplateSelector.value = true;
};

const handleTemplateSelect = (template: any) => {
  selectedTemplate.value = template;
  showTemplateSelector.value = false;
  showTemplateEmailForm.value = true;
};

const closeTemplateSelector = () => {
  showTemplateSelector.value = false;
  selectedColumnId.value = '';
  selectedColumnTitle.value = '';
};

const closeTemplateEmailForm = () => {
  showTemplateEmailForm.value = false;
  selectedTemplate.value = null;
  selectedColumnId.value = '';
  selectedColumnTitle.value = '';
};

const handleTemplateEmailCreate = (emailData: any) => {
  console.log('Creating email from template:', emailData);
  boardStore.addEmail(emailData.columnId, {
    id: `email-${Date.now()}`,
    subject: emailData.subject,
    from: emailData.from,
    content: emailData.content,
    priority: emailData.priority,
    tags: emailData.tags,
    date: new Date().toISOString(),
    assignedTo: '',
    isRead: false
  });
  closeTemplateEmailForm();
};

const openImportDialog = (columnId: string) => {
  selectedColumnId.value = columnId;
  const column = columns.value.find(col => col.id === columnId);
  selectedColumnTitle.value = column?.title || 'Column';
  showImportModal.value = true;
};

const closeImportModal = () => {
  showImportModal.value = false;
  selectedColumnId.value = '';
  selectedColumnTitle.value = '';
};

const handleContactImport = (contacts: any[]) => {
  console.log('Importing contacts:', contacts);

  contacts.forEach((contactData, index) => {
    boardStore.addEmail(selectedColumnId.value, {
      id: `imported-${Date.now()}-${index}`,
      subject: `Contact: ${contactData.name}`,
      from: contactData.email,
      content: `Name: ${contactData.name}\nCompany: ${contactData.company}\nPhone: ${contactData.phone}\nNotes: ${contactData.notes}`,
      priority: contactData.priority,
      tags: ['imported', 'contact'],
      date: new Date().toISOString(),
      assignedTo: '',
      isRead: false
    });
  });

  closeImportModal();
};

// Column Management Functions
const openColumnManagement = (columnId: string) => {
  selectedColumnId.value = columnId;
  const column = columns.value.find(col => col.id === columnId);
  selectedColumnTitle.value = column?.title || 'Column';
  showColumnManagement.value = true;
};

const closeColumnManagement = () => {
  showColumnManagement.value = false;
  selectedColumnId.value = '';
  selectedColumnTitle.value = '';
};

const openSequenceBuilder = () => {
  showColumnManagement.value = false;
  showOnePageSequenceBuilder.value = true;
  selectedSequence.value = null; // Create new sequence
};

const closeSequenceManagement = () => {
  showSequenceManagement.value = false;
  selectedSequence.value = null;
};

const openBroadcastBuilder = () => {
  showColumnManagement.value = false;
  showBroadcastManagement.value = true;
};

const closeBroadcastManagement = () => {
  showBroadcastManagement.value = false;
  selectedBroadcast.value = null;
};

const handleEditContact = (contact: any) => {
  console.log('Edit contact:', contact);
  // Open contact edit modal
};

const handleEditSequence = () => {
  showSequenceManagement.value = false;
  showOnePageSequenceBuilder.value = true;
  // selectedSequence.value already contains the sequence to edit
};

const handleEnrollContacts = () => {
  console.log('Enroll contacts to sequence:', selectedSequence.value);
  // Open contact selection modal
};

const handleEditBroadcast = () => {
  console.log('Edit broadcast:', selectedBroadcast.value);
  // Open broadcast builder with edit mode
};

const handleSendBroadcast = () => {
  console.log('Send broadcast:', selectedBroadcast.value);
  // Send broadcast immediately
};

const handleScheduleBroadcast = () => {
  console.log('Schedule broadcast:', selectedBroadcast.value);
  // Open schedule picker
};

// One-Page Sequence Builder Functions
const closeOnePageSequenceBuilder = () => {
  showOnePageSequenceBuilder.value = false;
  selectedSequence.value = null;
};

const handleSequenceSave = (sequence: any) => {
  console.log('Saving sequence:', sequence);
  // TODO: Save sequence to backend
  // Update local state if editing existing sequence
  closeOnePageSequenceBuilder();
};

const openSequenceBuilderFromColumn = (columnId: string) => {
  selectedColumnId.value = columnId;
  const column = columns.value.find((col: any) => col.id === columnId);
  selectedColumnTitle.value = column?.title || 'Column';
  selectedSequence.value = null; // Create new sequence
  showOnePageSequenceBuilder.value = true;
};

const viewColumnAnalytics = (columnId: string) => {
  console.log('View analytics for column:', columnId);
  // Implement column analytics
};

const exportColumnData = (columnId: string) => {
  console.log('Export data for column:', columnId);
  // Implement column data export
};

const toggleMultiSelect = () => {
  boardStore.setMultiSelectMode(!isMultiSelectMode.value);
};

const toggleSortOrder = () => {
  const newOrder = sortOrder.value === 'desc' ? 'asc' : 'desc';
  boardStore.setSorting(sortBy.value, newOrder);
};

const clearFilters = () => {
  searchQuery.value = '';
  priorityFilter.value = '';
  assigneeFilter.value = '';
  boardStore.setFilters({
    search: '',
    priority: '',
    assignee: '',
    tags: []
  });
};

const handleEmailReply = (email: EmailCard) => {
  console.log('Reply to email:', email);
  // Implement email reply
  closeEmailDetail();
};

const handleEmailForward = (email: EmailCard) => {
  console.log('Forward email:', email);
  // Implement email forward
  closeEmailDetail();
};

const handleEmailDelete = (emailId: string) => {
  boardStore.deleteEmail(emailId);
  closeEmailDetail();
};

// Keyboard shortcuts
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape') {
    if (showEmailDetail.value) {
      closeEmailDetail();
    } else if (isMultiSelectMode.value) {
      boardStore.setMultiSelectMode(false);
    }
  } else if (event.key === 'a' && (event.ctrlKey || event.metaKey)) {
    event.preventDefault();
    boardStore.selectAllEmails();
  } else if (event.key === 'd' && (event.ctrlKey || event.metaKey)) {
    event.preventDefault();
    if (boardStore.selectedEmails.size > 0) {
      const selectedIds = Array.from(boardStore.selectedEmails);
      if (confirm(`Delete ${selectedIds.length} email(s)?`)) {
        boardStore.bulkDeleteEmails(selectedIds);
      }
    }
  }
};

// Add keyboard event listener
document.addEventListener('keydown', handleKeydown);
</script>
