<template>
  <div class="flex-1 p-6">
    <div class="flex items-center justify-center h-full">
      <div class="text-center">
        <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <component :is="getIcon()" class="w-12 h-12 text-gray-400" />
        </div>
        <h2 class="text-2xl font-bold text-gray-900 mb-2">{{ getTitle() }}</h2>
        <p class="text-gray-600 mb-4">Halaman ini sedang dalam pengembangan</p>
        <router-link
          to="/"
          class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          Kembali ke Dashboard
        </router-link>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRoute } from 'vue-router';
import { FileText, Zap, Calendar, Archive, Bookmark } from 'lucide-vue-next';

const route = useRoute();

const getTitle = () => {
  switch (route.name) {
    case 'templates': return 'Template Email';
    case 'automation': return 'Otomatisasi';
    case 'calendar': return 'Kalender';
    case 'archive': return 'Arsip';
    case 'bookmarks': return 'Bookmark';
    default: return 'Halaman';
  }
};

const getIcon = () => {
  switch (route.name) {
    case 'templates': return FileText;
    case 'automation': return Zap;
    case 'calendar': return Calendar;
    case 'archive': return Archive;
    case 'bookmarks': return Bookmark;
    default: return FileText;
  }
};
</script>
